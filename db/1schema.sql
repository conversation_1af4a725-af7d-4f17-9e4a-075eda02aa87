-- ydsf 核心表
create database `ydsfx` default character set utf8mb4 collate utf8mb4_general_ci;

-- ydsf 工作流相关库
create database `ydsfx_flow` default character set utf8mb4 collate utf8mb4_general_ci;

-- ydsf 任务相关库
create database `ydsfx_job` default character set utf8mb4 collate utf8mb4_general_ci;

-- ydsf 公众号管理相关库
create database `ydsfx_mp` default character set utf8mb4 collate utf8mb4_general_ci;

-- ydsf nacos配置相关库
create database `ydsfx_config` default character set utf8mb4 collate utf8mb4_general_ci;

-- ydsf pay配置相关库
create database `ydsfx_pay` default character set utf8mb4 collate utf8mb4_general_ci;

-- ydsf codegen相关库
create database `ydsfx_codegen` default character set utf8mb4 collate utf8mb4_general_ci;

-- ydsf report相关库
create database `ydsfx_report` default character set utf8mb4 collate utf8mb4_general_ci;

-- ydsf bi 报表相关的数据库
create database `ydsfx_bi` default character set utf8mb4 collate utf8mb4_general_ci;

-- ydsf app 模块相关的数据库
create database `ydsfx_app` default character set utf8mb4 collate utf8mb4_general_ci;
