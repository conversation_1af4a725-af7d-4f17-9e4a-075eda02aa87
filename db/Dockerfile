FROM registry.cn-hangzhou.aliyuncs.com/dockerhub_mirror/mysql-server:8.0.32

MAINTAINER ydsf(<EMAIL>)

ENV TZ=Asia/Shanghai
ENV LANG C.UTF-8

RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY ./1schema.sql /docker-entrypoint-initdb.d

COPY ./2ydsfx.sql /docker-entrypoint-initdb.d

COPY ./3ydsfx_flow.sql /docker-entrypoint-initdb.d

COPY ./4ydsfx_job.sql /docker-entrypoint-initdb.d

COPY ./5ydsfx_mp.sql /docker-entrypoint-initdb.d

COPY ./6ydsfx_config.sql /docker-entrypoint-initdb.d

COPY ./7ydsfx_pay.sql /docker-entrypoint-initdb.d

COPY ./8ydsfx_codegen.sql /docker-entrypoint-initdb.d

COPY ./99ydsfx_bi.sql /docker-entrypoint-initdb.d

COPY ./999ydsfx_app.sql /docker-entrypoint-initdb.d
