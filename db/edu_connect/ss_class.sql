-- 班级信息表结构变更
ALTER TABLE `ss_course`.`ss_class` MODIFY COLUMN `grade` tinyint NOT NULL COMMENT '年级(字典类型: grade)' AFTER `class_name`;
ALTER TABLE `ss_course`.`ss_class` MODIFY COLUMN `class_state` tinyint NOT NULL DEFAULT 0 COMMENT '班级状态(字典类型: class_state)' AFTER `grade`;
ALTER TABLE `ss_course`.`ss_class` MODIFY COLUMN `is_sync_xiaogj` tinyint NOT NULL DEFAULT 1 COMMENT '是否同步校管家(字典类型: is_sync_xiaogj)' AFTER `class_state`;
ALTER TABLE `ss_course`.`ss_class` MODIFY COLUMN `class_type` tinyint NOT NULL COMMENT '班级类型(字典类型: class_type)' AFTER `is_sync_xiaogj`;
ALTER TABLE `ss_course`.`ss_class` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;

-- 班级授权教室表结构变更
ALTER TABLE `ss_course`.`ss_class_auth_room` MODIFY COLUMN `appointment_status` tinyint NOT NULL COMMENT '预约状态(字典类型: class_appointment_status)' AFTER `class_time_ids`;

-- 班级授权校区学生表结构变更
ALTER TABLE `ss_course`.`ss_class_auth_room_student` ADD COLUMN `del_flag` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除标识' AFTER `modifer`;


-- 班级管理按钮权限
INSERT INTO `ydsfx_dev`.`sys_menu` (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES (1840279826938769410, '班级管理', NULL, '/eduConnect/education/classsystem/index', NULL, 1839110153710489601, 'ele-User', '1', 1, '0', NULL, '0', 'admin', '2024-09-29 14:37:56', 'admin', '2024-10-17 11:36:48', '0', 1);
INSERT INTO `ydsfx_dev`.`sys_menu` (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES (1840279827312062466, '查看', 'edusystem_ssClass_view', NULL, NULL, 1840279826938769410, NULL, '1', 1, '0', NULL, '1', 'admin', '2024-09-29 14:37:56', ' ', '2024-10-16 17:17:14', '0', 1);
INSERT INTO `ydsfx_dev`.`sys_menu` (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES (1840279827815378945, '新增', 'edusystem_ssClass_add', NULL, NULL, 1840279826938769410, NULL, '1', 1, '0', NULL, '1', 'admin', '2024-09-29 14:37:56', ' ', '2024-10-16 17:17:15', '0', 1);
INSERT INTO `ydsfx_dev`.`sys_menu` (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES (1840279828444524545, '编辑', 'edusystem_ssClass_edit', NULL, NULL, 1840279826938769410, NULL, '1', 1, '0', NULL, '1', 'admin', '2024-09-29 14:37:57', ' ', '2024-10-16 17:17:16', '0', 1);
INSERT INTO `ydsfx_dev`.`sys_menu` (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES (1840279828822011905, '删除', 'edusystem_ssClass_del', NULL, NULL, 1840279826938769410, NULL, '1', 1, '0', NULL, '1', 'admin', '2024-09-29 14:37:57', ' ', '2024-10-16 17:17:17', '0', 1);
INSERT INTO `ydsfx_dev`.`sys_menu` (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES (1840279829195304961, '导入导出', 'edusystem_ssClass_export', NULL, NULL, 1840279826938769410, NULL, '1', 1, '0', NULL, '1', 'admin', '2024-09-29 14:37:57', ' ', '2024-10-16 17:17:18', '0', 1);
INSERT INTO `ydsfx_dev`.`sys_menu` (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES (1840279829195304962, '结业', 'edusystem_ssClass_complete', NULL, NULL, 1840279826938769410, NULL, '1', 1, '0', NULL, '1', 'admin', '2024-09-29 14:37:57', ' ', '2024-10-16 17:17:19', '0', 1);
INSERT INTO `ydsfx_dev`.`sys_menu` (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES (1840279829195304963, '查看课程安排', 'edusystem_ssClass_classTime', NULL, NULL, 1840279826938769410, NULL, '1', 1, '0', NULL, '1', 'admin', '2024-09-29 14:37:57', ' ', '2024-10-16 17:17:20', '0', 1);
INSERT INTO `ydsfx_dev`.`sys_menu` (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES (1840279829195304964, '班级授权', 'edusystem_ssClass_auth_device', NULL, NULL, 1840279826938769410, NULL, '1', 1, '0', NULL, '1', 'admin', '2024-09-29 14:37:57', ' ', '2024-10-16 17:17:21', '0', 1);
