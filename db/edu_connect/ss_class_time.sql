-- 课次表结构变更

ALTER TABLE `ss_class_time` ADD COLUMN `attend_time_start_time` datetime DEFAULT '1970-01-01 00:00:00' COMMENT '课次开始时间(yyyy-MM-dd HH:mm:ss)';
ALTER TABLE `ss_class_time` ADD COLUMN `attend_time_end_time` datetime DEFAULT '1970-01-01 00:00:00' COMMENT '课次结束时间(yyyy-MM-dd HH:mm:ss)';


alter table sys_menu
    modify permission varchar(255) null comment '权限标识';

-- 读书会管理
INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '新增课次', 'edusystem_ssClassTime_direct_addClassTime', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '编辑课次', 'edusystem_ssClassTime_direct_updateClassTime', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '删除课次', 'edusystem_ssClassTime_direct_deleteClassTime', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '获取监课链接', 'edusystem_ssClassTime_getSupervisionUrl', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '生成上课码', 'edusystem_ssClassTime_genRoomTimeCode', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '获取上课码', 'edusystem_ssClassTime_getRoomTimeCode', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);

-- 点播课管理
INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '新增课次', 'edusystem_ssClassTime_vod_addClassTime', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);


INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '编辑课次', 'edusystem_ssClassTime_vod_updateClassTime', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '删除课次', 'edusystem_ssClassTime_vod_deleteClassTime', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);
