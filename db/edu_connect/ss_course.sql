ALTER TABLE `ss_course`.`b_appointment_course` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`b_appointment_course_detail` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_class_auth_room_student` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_class_time_auth_room` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_class_time_student` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_course_schedule` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_course_schedule_books` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_course_schedule_rule` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_device_audio_config` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_device_config` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_interaction_consequence` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_interaction_red_packet_setting` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_interaction_setting` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_interaction_setting_detail` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;
ALTER TABLE `ss_course`.`ss_screenshot_detail` ADD COLUMN `del_flag` tinyint NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除' AFTER `modifer`;