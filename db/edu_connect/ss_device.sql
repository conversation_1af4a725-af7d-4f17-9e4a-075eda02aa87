
--  删除设备号唯一的标识
drop index uni_device_no on ss_device;

create index uni_device_no
    on ss_device (device_no);

-- 按钮 SQL
INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), '查询', 'edusystem_ssDevice_view', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);


INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), '新增', 'edusystem_ssDevice_add', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);


INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), '修改', 'edusystem_ssDevice_edit', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);


INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), '删除', 'edusystem_ssDevice_del', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);


INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), '导出', 'edusystem_ssDevice_export', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '解绑', 'edusystem_ssDevice_unbind', NULL, NULL, @parentId, NULL, '0', 0, '0', '0', '1', 'admin',  sysdate(), 'admin',  sysdate(), '0', 1);

######################################################################################################################################################################

--枚举 设备类型SQL

SELECT @dictParentId := FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
 (@dictParentId, 'device_type', '设备类型', 'admin', ' ', sysdate(), NULL, NULL, '0', '0', 1);




INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '1', '主讲端', 'device_type', '主讲端', 1, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '2', '教室端', 'device_type', '教室端', 2, ' ', ' ', sysdate(), sysdate(), '', '0', 1);

--枚举 设备状态SQL

SELECT @dictParentId := FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
 (@dictParentId, 'device_state', '设备状态', 'admin', ' ', sysdate(), NULL, NULL, '0', '0', 1);




INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '0', '启用', 'device_state', '启用', 1, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '1', '禁用', 'device_state', '禁用', 2, ' ', ' ', sysdate(), sysdate(), '', '0', 1);

--枚举 设备是否激活SQL

SELECT @dictParentId := FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
 (@dictParentId, 'device_active', '设备是否激活', 'admin', ' ', sysdate(), NULL, NULL, '0', '0', 1);




INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '0', '未激活', 'device_active', '未激活', 1, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '1', '已激活', 'device_active', '已激活', 2, ' ', ' ', sysdate(), sysdate(), '', '0', 1);

--枚举 设备欠费状态SQL

SELECT @dictParentId := FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
 (@dictParentId, 'device_arrears', '设备欠费状态', 'admin', ' ', sysdate(), NULL, NULL, '0', '0', 1);




INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '0', '正常', 'device_arrears', '正常', 1, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '1', '其他状态为欠费', 'device_arrears', '其他状态为欠费', 2, ' ', ' ', sysdate(), sysdate(), '', '0', 1);

--枚举 是否在线SQL

SELECT @dictParentId := FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
 (@dictParentId, 'is_on_line', '是否在线', 'admin', ' ', sysdate(), NULL, NULL, '0', '0', 1);




INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '0', '否', 'is_on_line', '否', 1, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '1', '是', 'is_on_line', '是', 2, ' ', ' ', sysdate(), sysdate(), '', '0', 1);

--枚举 设备是否永久SQL

SELECT @dictParentId := FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
 (@dictParentId, 'indate_forever', '设备是否永久', 'admin', ' ', sysdate(), NULL, NULL, '0', '0', 1);




INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '0', '否', 'indate_forever', '否', 1, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '1', '是', 'indate_forever', '是', 2, ' ', ' ', sysdate(), sysdate(), '', '0', 1);

--枚举 主讲端录课方式SQL

SELECT @dictParentId := FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
 (@dictParentId, 'agora_recording_type', '主讲端录课方式', 'admin', ' ', sysdate(), NULL, NULL, '0', '0', 1);




INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '0', '页面录制', 'agora_recording_type', '页面录制', 1, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '1', '云端录制', 'agora_recording_type', '云端录制', 2, ' ', ' ', sysdate(), sysdate(), '', '0', 1);

--枚举 终端SDK版本SQL

SELECT @dictParentId := FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
 (@dictParentId, 'sdk_type', '终端SDK版本', 'admin', ' ', sysdate(), NULL, NULL, '0', '0', 1);




INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '0', 'webSDK', 'sdk_type', 'webSDK', 1, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '1', 'Electron', 'sdk_type', 'Electron', 2, ' ', ' ', sysdate(), sysdate(), '', '0', 1);

--枚举 是否删除SQL

SELECT @dictParentId := FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
 (@dictParentId, 'del_flag', '是否删除', 'admin', ' ', sysdate(), NULL, NULL, '0', '0', 1);




INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '0', '未删除', 'del_flag', '未删除', 1, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`) VALUES
(FLOOR(RAND() * 1000000), @dictParentId, '1', '已删除', 'del_flag', '已删除', 2, ' ', ' ', sysdate(), sysdate(), '', '0', 1);


###########################################################################################################################################################################


alter table ss_course.ss_device_audio_config
    add del_flag char default '0' null comment '是否删除:0-未删除;1-已删除';

alter table ss_course.ss_device_config
    add del_flag char default '0' null comment '是否删除:0-未删除;1-已删除';


-- 插入到ss_class_room中

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间1', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间2', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间3', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间4', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间5', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间6', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间7', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间8', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间9', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间10', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间11', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间12', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间13', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间14', 0, 1, now(), 'sys', now(), 'sys');

INSERT INTO ss_course.ss_class_room (xgj_class_room_id, campus_id, class_room_name, class_room_state, class_room_type, ctime, creator, mtime, modifer)
VALUES (null, 1, '直播间15', 0, 1, now(), 'sys', now(), 'sys');

-- 为直播间创建父字典条目
SET @dictParentId = FLOOR(RAND() * 1000000);

INSERT INTO sys_dict (`id`, `dict_type`, `description`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `system_flag`, `del_flag`, `tenant_id`) VALUES
    (@dictParentId, 'live_room', '直播间教室', 'admin', ' ', SYSDATE(), NULL, NULL, '0', '0', 1);

-- 插入15个直播间条目
INSERT INTO sys_dict_item (`id`, `dict_id`, `item_value`, `label`, `dict_type`, `description`, `sort_order`, `create_by`, `update_by`, `create_time`, `update_time`, `remarks`, `del_flag`, `tenant_id`)
VALUES
    (FLOOR(RAND() * 1000000), @dictParentId, '1', '直播间1', 'live_room', '直播间1', 1, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '2', '直播间2', 'live_room', '直播间2', 2, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '3', '直播间3', 'live_room', '直播间3', 3, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '4', '直播间4', 'live_room', '直播间4', 4, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '5', '直播间5', 'live_room', '直播间5', 5, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '6', '直播间6', 'live_room', '直播间6', 6, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '7', '直播间7', 'live_room', '直播间7', 7, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '8', '直播间8', 'live_room', '直播间8', 8, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '9', '直播间9', 'live_room', '直播间9', 9, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '10', '直播间10', 'live_room', '直播间10', 10, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '11', '直播间11', 'live_room', '直播间11', 11, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '12', '直播间12', 'live_room', '直播间12', 12, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '13', '直播间13', 'live_room', '直播间13', 13, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '14', '直播间14', 'live_room', '直播间14', 14, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1),
    (FLOOR(RAND() * 1000000), @dictParentId, '15', '直播间15', 'live_room', '直播间15', 15, ' ', ' ', SYSDATE(), SYSDATE(), '', '0', 1);

