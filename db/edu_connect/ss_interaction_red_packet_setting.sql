-- 新增 门店红包规则设置表
CREATE TABLE `ss_interaction_red_packet_setting`  (
                                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                      `source` bigint NOT NULL COMMENT '飞天账号ID',
                                                      `xgj_campus_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '校管家校区ID',
                                                      `red_packet_number` int NOT NULL COMMENT '红包总分数',
                                                      `red_packet_upper_limit` int NOT NULL COMMENT '红包分数上限',
                                                      `red_packet_lower_limit` int NOT NULL COMMENT '红包分数下限',
                                                      `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                      `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
                                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                      `del_flag` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 0-未删除;1-已删除',
                                                      PRIMARY KEY (`id`) USING BTREE,
                                                      INDEX `idx_source`(`source`) USING BTREE,
                                                      INDEX `idx_xgj_campus_id`(`xgj_campus_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '门店红包规则设置表' ROW_FORMAT = Dynamic;
