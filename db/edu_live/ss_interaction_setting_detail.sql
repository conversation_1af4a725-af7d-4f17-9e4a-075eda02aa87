create table ss_course.ss_interaction_setting_detail
(
    id                     bigint auto_increment comment '主键id'
        primary key,
    interaction_setting_id bigint                                     not null comment '互动设置id',
    action_time            bigint                                     not null comment '触发时间点(视频播放到几秒触发)',
    interaction_duration   int              default 10                null comment '互动持续时间(单位秒)',
    create_by              varchar(255)                               null comment '创建人',
    create_time            datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by              varchar(255)                               null comment '修改人',
    update_time            datetime         default CURRENT_TIMESTAMP not null comment '修改时间',
    del_flag               tinyint unsigned default '0'               not null comment '是否删除:0-否;1-是'
)
    comment '互动明细表';


alter table ss_interaction_setting_detail
    add interaction_args varchar(500) null comment '触发互动时参数' after interaction_duration;



