CREATE TABLE `store_campus_joint`
(
    `id`                    bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `org`                   int                                                           DEFAULT NULL COMMENT '组织ID',
    `signing_subject`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '签约主体',
    `customer_name`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户名称',
    `signing_subject_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  DEFAULT NULL COMMENT '签约主体手机号',
    `department_id`         int                                                           DEFAULT NULL COMMENT '部门ID',
    `department_name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '部门名称',
    `del_flag`              tinyint                                                       DEFAULT '0' COMMENT '是否删除',
    `create_by`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    `create_time`           datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime                                                      DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='联营门店附加信息表';


INSERT INTO `store_campus_joint` (`id`, `org`, `signing_subject`, `customer_name`, `signing_subject_phone`, `department_id`, `department_name`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (4, 11, '泰安泰悦读文化科技有限公司', '联营客户', '15069897177', 62, '丽景儿童书店', 0, NULL, NULL, NULL, NULL);
INSERT INTO `store_campus_joint` (`id`, `org`, `signing_subject`, `customer_name`, `signing_subject_phone`, `department_id`, `department_name`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (5, 12, '泰安岱悦知行文化科技有限公司', '联营客户', '18263885830', 63, '望山儿童书店', 0, NULL, NULL, NULL, NULL);
INSERT INTO `store_campus_joint` (`id`, `org`, `signing_subject`, `customer_name`, `signing_subject_phone`, `department_id`, `department_name`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (462, 8, '临沂天才少年约读文化有限责任公司', '联营客户', '18615632724', 59, '银雀山儿童书店', 0, NULL, NULL, NULL, NULL);
INSERT INTO `store_campus_joint` (`id`, `org`, `signing_subject`, `customer_name`, `signing_subject_phone`, `department_id`, `department_name`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (627, 9, '临沂一期一会约读文化科技有限公司', '联营客户', '13475338780', 60, '大剧院儿童书店', 0, NULL, NULL, NULL, NULL);
INSERT INTO `store_campus_joint` (`id`, `org`, `signing_subject`, `customer_name`, `signing_subject_phone`, `department_id`, `department_name`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (628, 6, '徐州墨香书韵文化科技有限公司', '联营客户', '15866696050', 56, '橡树湾儿童书店', 0, NULL, NULL, NULL, NULL);
INSERT INTO `store_campus_joint` (`id`, `org`, `signing_subject`, `customer_name`, `signing_subject_phone`, `department_id`, `department_name`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (629, 10, '临沂万卷阁约读文化科技有限公司', '联营客户', '13153178687', 61, '天泰华府儿童书店 ', 0, NULL, NULL, NULL, NULL);
INSERT INTO `store_campus_joint` (`id`, `org`, `signing_subject`, `customer_name`, `signing_subject_phone`, `department_id`, `department_name`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (630, 7, '徐州墨香童悦文化科技有限公司', '联营客户', '18652268913', 58, '保利鑫城儿童书店', 0, NULL, NULL, NULL, NULL);
INSERT INTO `store_campus_joint` (`id`, `org`, `signing_subject`, `customer_name`, `signing_subject_phone`, `department_id`, `department_name`, `del_flag`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES (631, 13, '徐州墨香童心文化科技有限公司', '联营客户', '17362227092', 64, '人才家园门店', 0, NULL, NULL, NULL, NULL);
