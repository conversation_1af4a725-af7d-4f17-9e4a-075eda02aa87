CREATE TABLE `store_joint_bill`
(
    `id`             bigint NOT NULL COMMENT '主键ID',
    `school_id`      bigint NOT NULL COMMENT '校区ID',
    `store_id`       bigint NOT NULL COMMENT '门店ID',
    `student_id`     bigint NOT NULL COMMENT '学生ID',
    `course_type`    int    NOT NULL COMMENT '课程类型',
    `fee_date`       date                                                          DEFAULT NULL COMMENT '收费日期',
    `advisor_id`     bigint                                                        DEFAULT NULL COMMENT '业绩归属人',
    `total_amount`   decimal(10, 4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '总金额',
    `status`         tinyint unsigned DEFAULT '0' COMMENT '制单状态',
    `pay_type`       tinyint                                                       DEFAULT NULL COMMENT '支付方式',
    `pay_method`     tinyint                                                       DEFAULT NULL COMMENT '支付类型',
    `notes`          varchar(255)                                                  DEFAULT '' COMMENT '备注',
    `joint_pay_type` int                                                           DEFAULT NULL COMMENT '联营收费类型',
    `finance_id`     bigint                                                        DEFAULT NULL COMMENT '财务返回ID',
    `message`        varchar(255)                                                  DEFAULT NULL COMMENT '失败原因',
    `del_flag`       tinyint                                                       DEFAULT '0' COMMENT '是否删除',
    `create_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    `create_time`    datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime                                                      DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY              `idx_student_id` (`student_id`),
    KEY              `idx_school_id` (`school_id`,`student_id`),
    KEY              `idx_store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='联营财务收费单记录表';