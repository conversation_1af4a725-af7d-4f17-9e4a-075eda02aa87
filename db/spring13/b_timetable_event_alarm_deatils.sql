CREATE TABLE `b_timetable_event_alarm_deatils` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `store_id` bigint DEFAULT NULL COMMENT '门店ID',
    `timeable_id` bigint DEFAULT NULL COMMENT '上课课次ID',
    `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
    `event_describe` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '事件描述',
    `event_time` datetime DEFAULT NULL COMMENT '事件时间',
    `event_type` int DEFAULT NULL COMMENT '事件状态标识',
    `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
    `lession_no` bigint DEFAULT NULL COMMENT '上课课号',
    PRIMARY KEY (`id`)
)