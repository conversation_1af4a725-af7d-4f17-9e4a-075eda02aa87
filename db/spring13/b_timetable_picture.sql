CREATE TABLE `b_timetable_picture` (
        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `store_id` bigint DEFAULT NULL COMMENT '门店ID',
        `timeable_id` bigint DEFAULT NULL COMMENT '上课课次ID',
        `device_id` bigint DEFAULT NULL COMMENT '拍照设备ID',
        `photo_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '拍照照片url',
        `recognition_total_num` int DEFAULT '0' COMMENT '识别总数量',
        `recognition_children_num` int DEFAULT '0' COMMENT '识别幼儿数量',
        `recognition_teenagers_num` int DEFAULT '0' COMMENT '识别青少年数量',
        `recognition_youth_num` int DEFAULT '0' COMMENT '识别青年数量',
        `recognition_middle_num` int DEFAULT '0' COMMENT '识别中年数量',
        `recognition_elderly_num` int DEFAULT '0' COMMENT '识别老年数量',
        `recognition_human_num` int DEFAULT '0' COMMENT '识别到人体数量',
        `recognition_status` tinyint DEFAULT '0' COMMENT '识别状态: 0-未处理; 1-处理成功; 2-处理失败',
        `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
        `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
        `sort` int DEFAULT '0' COMMENT '计数第几张',
        `duration` int DEFAULT '0' COMMENT '距离上课时长',
        `lession_no` bigint DEFAULT NULL COMMENT '上课课号',
        `remark` varchar(255) DEFAULT NULL COMMENT '备注',
        PRIMARY KEY (`id`)
)