CREATE TABLE `ss_information` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                  `del_flag` tinyint unsigned DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                                  `contents_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '目录名称',
                                  `pid` bigint DEFAULT NULL COMMENT '上级id',
                                  `is_root` int DEFAULT '0' COMMENT '是否根节点: 0-否; 1-是',
                                  PRIMARY KEY (`id`)
)