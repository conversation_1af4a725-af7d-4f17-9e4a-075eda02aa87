CREATE TABLE `ss_information_auth_store_history` (
                                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                     `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                                     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                     `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                     `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                     `del_flag` tinyint unsigned DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                                                     `store_id` bigint DEFAULT NULL COMMENT '门店id',
                                                     `information_id` bigint DEFAULT NULL COMMENT '资料ID',
                                                     `opt_type` int DEFAULT '0' COMMENT '操作类型: 0-添加; 1-删除',
                                                     PRIMARY KEY (`id`)
)