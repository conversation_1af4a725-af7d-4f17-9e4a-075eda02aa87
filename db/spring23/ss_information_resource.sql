CREATE TABLE `ss_information_resource` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
                                           `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                           `del_flag` tinyint unsigned DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                                           `resource_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资源名',
                                           `resource_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资源路径',
                                           `is_download` int DEFAULT '0' COMMENT '是否可下载: 0-否; 1-是',
                                           `resource_type` int DEFAULT '0' COMMENT '资源类型: 0-其它; 1-在线文档; 2-视频; 3-音频; 4-图片;',
                                           `information_id` bigint DEFAULT NULL COMMENT '目录ID',
                                           PRIMARY KEY (`id`)
)