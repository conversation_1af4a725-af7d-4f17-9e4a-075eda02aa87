create table b_class_time_student
(
    id   bigint auto_increment comment '主键ID'
        primary key,
    store_id  bigint not null comment '门店ID',
    student_id bigint null comment '学生id',
    student_type int null comment '学生课次类型:1-班级学生;2-调课学生;3-添加考勤学生',
    lesson_no bigint null comment '课次编号',
    check_in_status int null comment '考勤状态:0-未出勤(缺勤);1-已考勤',
    check_in_type int null comment '考勤类型:0-正常签到;1-补签',
    check_in_time datetime null comment '考勤时间',
    check_in_create_by varchar(255) null comment '考勤操作人',
    create_by             varchar(255)                               null comment '创建人',
    create_time           datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by             varchar(255)                               null comment '修改人',
    update_time           datetime         default CURRENT_TIMESTAMP not null comment '修改时间',
    del_flag              tinyint unsigned default '0'               not null comment '是否删除: 0-否; 1-是;',
    is_regular_students int NULL DEFAULT NULL COMMENT '是否是正式学员: 1-是; 0-否;'
)
    comment '课次学生表';

-- 1. 课次考勤联合索引(最常用查询)
CREATE INDEX idx_lesson_student ON b_class_time_student(lesson_no, student_id);

-- 2. 门店课次联合索引(查询课次出勤情况)
CREATE INDEX idx_store_lesson ON b_class_time_student(store_id, lesson_no);

-- 3. 门店学生联合索引(查询学生考勤记录)
CREATE INDEX idx_store_student ON b_class_time_student(store_id, student_id);

-- 4. 考勤状态索引(查询未考勤/已考勤)
CREATE INDEX idx_check_status ON b_class_time_student(check_in_status);

-- 5. 数据统计索引(查询试听/正式学员)
CREATE INDEX idx_is_regular_students ON b_class_time_student(is_regular_students);

alter table b_class_time_student
    alter column check_in_status set default 0;

alter table b_class_time_student
    add adjust_status int null comment '是否调出:0-未调出;1-已调出' after check_in_type;

alter table b_class_time_student
    alter column adjust_status set default 0;


