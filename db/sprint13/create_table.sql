create table ss_video_play_token
(
    id           int auto_increment comment '主键'
        primary key,
    use_count    int              default 0                 null comment 'Token的有效使用次数',
    token        varchar(200)                               not null comment 'token',
    token_expire datetime                                   not null comment 'token过期时间，方便排查问题。',
    play_url     varchar(255)                               not null comment '视频播放链接',
    video_id     varchar(255)     default ''                not null comment '点播视频ID，阿里云点播视频ID',
    campus_id    bigint           default 0                 not null comment '校区ID',
    campus_name  varchar(255)                               null comment '校区名称',
    create_by    varchar(255)                               null comment '创建人',
    create_time  datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by    varchar(255)                               null comment '修改人',
    update_time  datetime         default CURRENT_TIMESTAMP not null comment '修改时间',
    del_flag     tinyint unsigned default '0'               not null comment '是否删除: 0-否; 1-是;',
    constraint ss_vod_play_token_token_uindex
        unique (token)
)
    comment '视频播放凭证';

