create table b_interaction_receiver_clicker
(
    id                 bigint comment '主键ID',
    receiver_sn_number varchar(255)                       not null comment '接收器sn码',
    serial_number      int                                not null comment '自增序号',
    sn_number          varchar(255)                       not null comment '答题器SN码',
    clicker_state      tinyint                            not null comment '答题器状态: 0-启用; 1-禁用;',
    create_by          varchar(255) null comment '创建人',
    create_time        datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by          varchar(255) null comment '修改人',
    update_time        datetime default CURRENT_TIMESTAMP not null comment '修改时间',
    del_flag           tinyint unsigned default '0'               not null comment '是否删除: 0-否; 1-是;'
) comment '互动答题器' collate = utf8mb4_general_ci;

alter table b_interaction_receiver_clicker
    add constraint b_interaction_receiver_clicker_pk
        primary key (id);

alter table b_interaction_receiver_clicker
    modify id bigint auto_increment comment '主键ID';

alter table b_interaction_receiver_clicker
    auto_increment = 1;

create index b_interaction_sn_number_index
    on b_interaction_receiver_clicker (receiver_sn_number);

create index b_interaction_receiver_clicker_sn_number_index
    on b_interaction_receiver_clicker (sn_number);

alter table b_interaction_receiver_clicker
    alter column clicker_state set default 0;



create table b_interaction_receiver
(
    id             bigint comment '主键ID',
    store_id       bigint                             not null comment '门店ID',
    classroom_id   bigint null comment '教室ID',
    sn_number      varchar(255)                       not null comment '接收器sn码',
    model_no       varchar(255)                       not null comment '接收器型号',
    aisle          bigint                             not null comment '接收器通道号',
    receiver_state tinyint                            not null comment '接收器状态: 0-启用; 1-禁用;',
    create_by      varchar(255) null comment '创建人',
    create_time    datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by      varchar(255) null comment '修改人',
    update_time    datetime default CURRENT_TIMESTAMP not null comment '修改时间',
    del_flag       tinyint unsigned default '0'               not null comment '是否删除: 0-否; 1-是;'
) comment '互动接收器' collate = utf8mb4_general_ci;


alter table b_interaction_receiver
    add constraint b_interaction_receiver_pk
        primary key (id);

alter table b_interaction_receiver
    modify id bigint auto_increment comment '主键ID';

alter table b_interaction_receiver
    auto_increment = 1;

create index b_interaction_receiver_sn_number_index
    on b_interaction_receiver (sn_number);

alter table b_interaction_receiver
    alter column receiver_state set default 0;

alter table b_interaction_receiver
    modify classroom_id bigint not null comment '教室ID';

alter table b_class_time_student
    add receiver_sn_number varchar(255) null comment '绑定接收器SN码' after check_in_create_by;

alter table b_class_time_student
    add clicker_sn_number varchar(255) null comment '绑定答题器SN码' after receiver_sn_number;









