
-- ----------------------------
-- Table structure for paper_child_parent_module
-- ----------------------------
DROP TABLE IF EXISTS `paper_child_parent_module`;
CREATE TABLE `paper_child_parent_module`  (
                                              `children_module_id` int NOT NULL AUTO_INCREMENT,
                                              `paper_user_id` int NOT NULL COMMENT '测评用户id',
                                              `paper_id` int NOT NULL COMMENT '试卷id',
                                              `module_id` int NULL DEFAULT 0 COMMENT '模块id（字典）',
                                              `score` int NULL DEFAULT 0 COMMENT '得分',
                                              `answers` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模块等级名称',
                                              `goodness` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '优势',
                                              `lessness` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '不足',
                                              `img_type` tinyint NULL DEFAULT 1 COMMENT '1雷达2气泡3条形',
                                              `img_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '出图信息',
                                              `created_at` datetime NULL DEFAULT NULL,
                                              `updated_at` datetime NULL DEFAULT NULL,
                                              `deleted_at` datetime NULL DEFAULT NULL,
                                              `role_type` tinyint NULL DEFAULT 1 COMMENT '1儿童2家长',
                                              `answer_id` int NULL DEFAULT 0 COMMENT '主表id',
                                              `read_assessment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '阅读理解考核点信息',
                                              PRIMARY KEY (`children_module_id`) USING BTREE,
                                              INDEX `id_paper_user_id`(`paper_user_id`) USING BTREE,
                                              INDEX `idx_answer_id`(`answer_id`, `role_type`, `module_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 757096 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '儿童家长测评答题模块表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for paper_child_parent_option
-- ----------------------------
DROP TABLE IF EXISTS `paper_child_parent_option`;
CREATE TABLE `paper_child_parent_option`  (
                                              `children_option_id` int NOT NULL AUTO_INCREMENT,
                                              `paper_user_id` int NOT NULL COMMENT '测评用户id',
                                              `paper_id` int NOT NULL COMMENT '试卷id',
                                              `question_id` int NOT NULL COMMENT '题目id',
                                              `answers` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '答案选项（多个逗号间隔）',
                                              `answer_ids` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '答案id，逗号间隔',
                                              `module_id` int NULL DEFAULT 0 COMMENT '模块id（字典）',
                                              `score` int NULL DEFAULT 0 COMMENT '得分(可废弃)',
                                              `sort` int NOT NULL COMMENT '题目序号',
                                              `created_at` datetime NULL DEFAULT NULL,
                                              `updated_at` datetime NULL DEFAULT NULL,
                                              `deleted_at` datetime NULL DEFAULT NULL,
                                              `role_type` tinyint NULL DEFAULT 1 COMMENT '1儿童2家长',
                                              `answer_id` int NULL DEFAULT 0 COMMENT '主表id',
                                              `read_id` int NULL DEFAULT 0 COMMENT '阅读理解题目id',
                                              PRIMARY KEY (`children_option_id`) USING BTREE,
                                              INDEX `module_id`(`module_id`) USING BTREE,
                                              INDEX `answer_id`(`answer_id`) USING BTREE,
                                              INDEX `paper_id`(`paper_id`) USING BTREE,
                                              INDEX `read_id`(`read_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5943797 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '儿童家长测评答题选项表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for paper_children_answer
-- ----------------------------
DROP TABLE IF EXISTS `paper_children_answer`;
CREATE TABLE `paper_children_answer`  (
                                          `children_answer_id` int NOT NULL AUTO_INCREMENT,
                                          `paper_user_id` int NOT NULL COMMENT '线索id',
                                          `paper_id` int NOT NULL COMMENT '试卷id',
                                          `children_paper_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '试卷名',
                                          `children_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '儿童姓名',
                                          `test_starttime` datetime NULL DEFAULT NULL COMMENT '测评开始时间',
                                          `test_endtime` datetime NULL DEFAULT NULL COMMENT '测评结束时间',
                                          `test_score` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '测评总分',
                                          `time_level` int NOT NULL DEFAULT 0 COMMENT '测评时间段（字典）',
                                          `test_level` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '测评阶段（字典）',
                                          `test_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '测评时间（分钟）',
                                          `parent_id` int NOT NULL DEFAULT 0 COMMENT '关联家长试卷id',
                                          `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
                                          `optimization` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '阅读诊断优化词',
                                          `created_by` int UNSIGNED NULL DEFAULT 0 COMMENT '创建人',
                                          `created_at` datetime NULL DEFAULT NULL,
                                          `updated_at` datetime NULL DEFAULT NULL,
                                          `deleted_at` datetime NULL DEFAULT NULL,
                                          `age` int NULL DEFAULT 0 COMMENT '测试时的年龄',
                                          `grade_level` int NULL DEFAULT 0 COMMENT '测试试卷时的年级',
                                          `terminal_id` int NOT NULL DEFAULT 0 COMMENT '门店id',
                                          `is_report` tinyint(1) NULL DEFAULT 2 COMMENT '1生成报告',
                                          `read_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '试卷等级',
                                          `download_report` int NULL DEFAULT 0 COMMENT '报告下载次数',
                                          `download_test` int NULL DEFAULT 0 COMMENT '试卷下载次数',
                                          `right_num` int NULL DEFAULT 0 COMMENT '默认正确数',
                                          PRIMARY KEY (`children_answer_id`) USING BTREE,
                                          UNIQUE INDEX `unid`(`children_answer_id`) USING BTREE,
                                          INDEX `children_paper_name`(`children_paper_name`) USING BTREE,
                                          INDEX `paper_id`(`paper_id`) USING BTREE,
                                          INDEX `terminal_id`(`terminal_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 138761 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '儿童测评答题主表' ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Table structure for paper_module
-- ----------------------------
DROP TABLE IF EXISTS `paper_module`;
CREATE TABLE `paper_module`  (
                                 `paper_module_id` int NOT NULL AUTO_INCREMENT,
                                 `paper_id` int NOT NULL COMMENT '试卷id',
                                 `module_id` int NOT NULL COMMENT '模块id',
                                 `module_sort` int NOT NULL DEFAULT 255 COMMENT '模块排序',
                                 `order_quest` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '所有题目序号',
                                 `assessment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '阅读理解考核点逗号间隔',
                                 `img_type` tinyint NULL DEFAULT 1 COMMENT '出图类型1雷达2气泡3条形',
                                 `img_option` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '题目序号选项（逗号间隔）',
                                 `deleted_at` datetime NULL DEFAULT NULL,
                                 PRIMARY KEY (`paper_module_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '试卷模块表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for paper_parent_answer
-- ----------------------------
DROP TABLE IF EXISTS `paper_parent_answer`;
CREATE TABLE `paper_parent_answer`  (
                                        `parent_answer_id` int NOT NULL AUTO_INCREMENT,
                                        `paper_user_id` int NOT NULL COMMENT '线索id',
                                        `paper_id` int NOT NULL COMMENT '试卷id',
                                        `purpose` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '评测目的-家长',
                                        `parent_paper_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '家长试卷名',
                                        `parent_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '家长姓名',
                                        `relation_ship` tinyint NOT NULL DEFAULT 3 COMMENT '关系1母亲2父亲3其他',
                                        `profession` tinyint NULL DEFAULT 4 COMMENT '职业1教育2医疗3保险4其他',
                                        `test_starttime` datetime NULL DEFAULT NULL COMMENT '测评开始时间',
                                        `test_endtime` datetime NULL DEFAULT NULL COMMENT '测评结束时间',
                                        `test_score` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '测评总分',
                                        `time_level` int NOT NULL DEFAULT 0 COMMENT '测评时间段（字典）',
                                        `test_level` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '测评阶段（字典）',
                                        `test_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '测评时间（分钟）',
                                        `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
                                        `created_at` datetime NULL DEFAULT NULL,
                                        `updated_at` datetime NULL DEFAULT NULL,
                                        `deleted_at` datetime NULL DEFAULT NULL,
                                        `terminal_id` int NULL DEFAULT 0 COMMENT '门店id',
                                        `is_report` tinyint(1) NULL DEFAULT 2 COMMENT '1生成报告',
                                        `aditive` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '附加题、3-5个书名',
                                        `download_report` int NULL DEFAULT 0 COMMENT '报告下载次数',
                                        `download_test` int NULL DEFAULT 0 COMMENT '试卷下载次数',
                                        PRIMARY KEY (`parent_answer_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 94326 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '家长测评答题主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for paper_question
-- ----------------------------
DROP TABLE IF EXISTS `paper_question`;
CREATE TABLE `paper_question`  (
                                   `paper_question_id` int NOT NULL AUTO_INCREMENT,
                                   `question_id` int NOT NULL COMMENT '题目id',
                                   `paper_id` int NOT NULL COMMENT '试卷id',
                                   `module_id` int NOT NULL COMMENT '模块id',
                                   `module_sort` int NOT NULL DEFAULT 255 COMMENT '模块排序',
                                   `quest_sort` int NOT NULL DEFAULT 255 COMMENT '题目序号',
                                   `deleted_at` datetime NULL DEFAULT NULL,
                                   PRIMARY KEY (`paper_question_id`) USING BTREE,
                                   INDEX `paper_id`(`paper_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 188 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '试卷题目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for paper_terminal
-- ----------------------------
DROP TABLE IF EXISTS `paper_terminal`;
CREATE TABLE `paper_terminal`  (
                                   `paper_terminal_id` int NOT NULL AUTO_INCREMENT,
                                   `terminal_id` int NOT NULL DEFAULT 0 COMMENT '加盟商后台门店id',
                                   `terminal_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '门店名称',
                                   `shop_owner` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '店长',
                                   `test_num` int NULL DEFAULT 0 COMMENT '测评人数',
                                   `ff_num` int NULL DEFAULT 0 COMMENT '转化人数',
                                   `created_at` datetime NULL DEFAULT NULL,
                                   `updated_at` datetime NULL DEFAULT NULL,
                                   `deleted_at` datetime NULL DEFAULT NULL,
                                   `shop_phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '店长手机号',
                                   `or_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '店长二维码链接',
                                   `level_test_num` int NULL DEFAULT 0,
                                   PRIMARY KEY (`paper_terminal_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 679 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评测门店表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for paper_user
-- ----------------------------
DROP TABLE IF EXISTS `paper_user`;
CREATE TABLE `paper_user`  (
                               `paper_user_id` int NOT NULL AUTO_INCREMENT COMMENT '标识id',
                               `user_id` int UNSIGNED NULL DEFAULT 0 COMMENT '从java获取userid',
                               `parent_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '家长姓名',
                               `terminal_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '门店id',
                               `is_member` int NULL DEFAULT 2 COMMENT '是否飞飞(半年，1年，2年，季度)会员 1是2否',
                               `child_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '儿童姓名',
                               `phone` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号',
                               `sex` tinyint NULL DEFAULT 1 COMMENT '性别1男2女',
                               `birthday` date NULL DEFAULT NULL,
                               `purpose_level` int NULL DEFAULT 0 COMMENT '意向级别（字典）',
                               `test_number` int NULL DEFAULT 0 COMMENT '评测次数',
                               `new_level` int NULL DEFAULT 0 COMMENT '最新测评阶段',
                               `test_time` datetime NULL DEFAULT NULL COMMENT '最新测评时间',
                               `next_time` datetime NULL DEFAULT NULL COMMENT '下次跟进时间',
                               `status` tinyint NULL DEFAULT 1 COMMENT '线索状态1跟进2购卡3流失',
                               `card_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '所有购卡（逗号间隔）',
                               `created_at` datetime NULL DEFAULT NULL,
                               `updated_at` datetime NULL DEFAULT NULL,
                               `deleted_at` datetime NULL DEFAULT NULL,
                               `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
                               `grade_level` int NULL DEFAULT 0,
                               `relation_ship` tinyint NOT NULL DEFAULT 0 COMMENT '关系1母亲2父亲3其他',
                               `profession` tinyint NULL DEFAULT 0 COMMENT '职业1教育2医疗3保险4其他',
                               PRIMARY KEY (`paper_user_id`) USING BTREE,
                               UNIQUE INDEX `up_idx`(`terminal_id`, `user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 139911 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评测用户表' ROW_FORMAT = DYNAMIC;


ALTER TABLE `paper_user` 
ADD COLUMN `test_score` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '最新测评总分' AFTER `profession`,
ADD COLUMN `is_report` tinyint(1) NULL DEFAULT 0 COMMENT '1有需要生成报告的' AFTER `test_score`;
-- ----------------------------
-- Table structure for question
-- ----------------------------
DROP TABLE IF EXISTS `question`;
CREATE TABLE `question`  (
                             `question_id` int NOT NULL AUTO_INCREMENT,
                             `question_code` char(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '题号（时间戳加4位随机数）',
                             `question_type` tinyint NOT NULL DEFAULT 1 COMMENT '试题类型1单题2多题3阅读题',
                             `question_level` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '试题阶段',
                             `module_id` int NOT NULL COMMENT '模块名称id',
                             `question_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '题目内容',
                             `remarks` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
                             `assessment` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '（单、双/阅读)考核点',
                             `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '（阅读理解）文章标题',
                             `created_at` datetime NULL DEFAULT NULL,
                             `updated_at` datetime NULL DEFAULT NULL,
                             `deleted_at` datetime NULL DEFAULT NULL,
                             `created_by` int NULL DEFAULT NULL COMMENT '创建人',
                             `row` int NULL DEFAULT 1 COMMENT '行数',
                             `pad_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '(适配pad)题目内容',
                             PRIMARY KEY (`question_id`, `question_code`) USING BTREE,
                             UNIQUE INDEX `question_id`(`question_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 156 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '题目主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for question_image
-- ----------------------------
DROP TABLE IF EXISTS `question_image`;
CREATE TABLE `question_image`  (
                                   `image_id` int NOT NULL AUTO_INCREMENT,
                                   `img_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '图片地址',
                                   `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
                                   `question_id` int NOT NULL COMMENT '问题id',
                                   `created_at` datetime NULL DEFAULT NULL,
                                   `updated_at` datetime NULL DEFAULT NULL,
                                   `deleted_at` datetime NULL DEFAULT NULL,
                                   PRIMARY KEY (`image_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '题目图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for question_know
-- ----------------------------
DROP TABLE IF EXISTS `question_know`;
CREATE TABLE `question_know`  (
                                  `know_id` int NOT NULL AUTO_INCREMENT,
                                  `question_code` char(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '题号（时间戳加4位随机数）',
                                  `question_type` tinyint NOT NULL DEFAULT 1 COMMENT '试题类型1单题2多题3阅读题',
                                  `question_level` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '试题阶段',
                                  `grade` tinyint UNSIGNED NULL DEFAULT 1 COMMENT '年级',
                                  `question_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '题目内容',
                                  `question_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
                                  `created_at` datetime NULL DEFAULT NULL,
                                  `updated_at` datetime NULL DEFAULT NULL,
                                  `deleted_at` datetime NULL DEFAULT NULL,
                                  `created_by` int UNSIGNED NULL DEFAULT 0 COMMENT '创建人',
                                  PRIMARY KEY (`know_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '识字主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for question_know_option
-- ----------------------------
DROP TABLE IF EXISTS `question_know_option`;
CREATE TABLE `question_know_option`  (
                                         `know_option_id` int NOT NULL AUTO_INCREMENT,
                                         `know_id` int NOT NULL COMMENT '问题id',
                                         `grade` tinyint UNSIGNED NULL DEFAULT 1 COMMENT '年级',
                                         `option_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '选项内容',
                                         `option_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '选项值A/B/C',
                                         `score` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '分值',
                                         `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
                                         `created_at` datetime NULL DEFAULT NULL,
                                         `updated_at` datetime NULL DEFAULT NULL,
                                         `deleted_at` datetime NULL DEFAULT NULL,
                                         PRIMARY KEY (`know_option_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 165 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '识字题选项' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for question_option
-- ----------------------------
DROP TABLE IF EXISTS `question_option`;
CREATE TABLE `question_option`  (
                                    `option_id` int NOT NULL AUTO_INCREMENT,
                                    `question_id` int NOT NULL COMMENT '问题id',
                                    `option_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '选项内容',
                                    `option_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '注解',
                                    `option_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '选项值A/B/C',
                                    `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '分值',
                                    `sort` int NOT NULL DEFAULT 255 COMMENT '排序',
                                    `created_at` datetime NULL DEFAULT NULL,
                                    `updated_at` datetime NULL DEFAULT NULL,
                                    `deleted_at` datetime NULL DEFAULT NULL,
                                    PRIMARY KEY (`option_id`) USING BTREE,
                                    INDEX `question_id`(`question_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 748 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '题目选项表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for question_read
-- ----------------------------
DROP TABLE IF EXISTS `question_read`;
CREATE TABLE `question_read`  (
                                  `read_id` int NOT NULL AUTO_INCREMENT,
                                  `question_id` int NULL DEFAULT NULL COMMENT '问题id',
                                  `question_type` tinyint NULL DEFAULT 1 COMMENT '题目类型1单2多选',
                                  `test_point` int NOT NULL DEFAULT 0 COMMENT '考核点（数据字典选择）',
                                  `read_title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '题目内容',
                                  `read_answer` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '答案（多个的话逗号间隔）',
                                  `remarks` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
                                  `sort` int NULL DEFAULT 255 COMMENT '排序',
                                  `created_at` datetime NULL DEFAULT NULL,
                                  `updated_at` datetime NULL DEFAULT NULL,
                                  `deleted_at` datetime NULL DEFAULT NULL,
                                  `row` int NULL DEFAULT 1 COMMENT '行数',
                                  `read_title_py` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '题目内容拼音',
                                  PRIMARY KEY (`read_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 110 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '阅读理解问题表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for question_read_image
-- ----------------------------
DROP TABLE IF EXISTS `question_read_image`;
CREATE TABLE `question_read_image`  (
                                        `read_img_id` int NOT NULL AUTO_INCREMENT,
                                        `question_read_id` int NOT NULL COMMENT '阅读问题id',
                                        `img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '图片url',
                                        `read_sort` int NULL DEFAULT 255 COMMENT '排序',
                                        `created_at` datetime NULL DEFAULT NULL,
                                        `updated_at` datetime NULL DEFAULT NULL,
                                        `deleted_at` datetime NULL DEFAULT NULL,
                                        PRIMARY KEY (`read_img_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '阅读理解问题图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for question_read_option
-- ----------------------------
DROP TABLE IF EXISTS `question_read_option`;
CREATE TABLE `question_read_option`  (
                                         `read_option_id` int NOT NULL AUTO_INCREMENT,
                                         `question_id` int NULL DEFAULT NULL COMMENT '问题id',
                                         `question_read_id` int NULL DEFAULT NULL COMMENT '阅读问题id',
                                         `question_read_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '选项内容',
                                         `question_read_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '选项值A/B/C',
                                         `question_read_score` decimal(10, 2) NULL DEFAULT NULL COMMENT '分值',
                                         `read_sort` int NULL DEFAULT 255 COMMENT '排序',
                                         `created_at` datetime NULL DEFAULT NULL,
                                         `updated_at` datetime NULL DEFAULT NULL,
                                         `deleted_at` datetime NULL DEFAULT NULL,
                                         `content_py` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '选项内容',
                                         PRIMARY KEY (`read_option_id`) USING BTREE,
                                         INDEX `question_read_id`(`question_read_id`) USING BTREE,
                                         INDEX `question_read_score`(`question_read_score`) USING BTREE,
                                         INDEX `question_id`(`question_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 420 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '阅读理解问题选项表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_num_log
-- ----------------------------
DROP TABLE IF EXISTS `test_num_log`;
CREATE TABLE `test_num_log`  (
                                 `log_id` int NOT NULL AUTO_INCREMENT,
                                 `test_level` int NOT NULL COMMENT '阶段',
                                 `num` int NOT NULL DEFAULT 1 COMMENT '数量',
                                 `date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '时间',
                                 `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1测评数2转化数',
                                 `deleted_at` datetime NULL DEFAULT NULL,
                                 `user_id` int NOT NULL COMMENT '用户id',
                                 `terminal_id` int NOT NULL COMMENT '门店id',
                                 PRIMARY KEY (`log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 139301 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_paper
-- ----------------------------
DROP TABLE IF EXISTS `test_paper`;
CREATE TABLE `test_paper`  (
                               `test_id` int NOT NULL AUTO_INCREMENT,
                               `test_code` char(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '试卷号（时间戳加4位随机）',
                               `school_age` int NULL DEFAULT 0 COMMENT '学龄阶段（数据字典存）-废弃',
                               `question_num` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '题目A+B',
                               `paper_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '试卷名称',
                               `role_type` tinyint NOT NULL DEFAULT 1 COMMENT '1儿童2家长',
                               `age_type` tinyint NULL DEFAULT 2 COMMENT '类别1学龄前2学龄后',
                               `time_limit` int NULL DEFAULT 0 COMMENT '时间限制',
                               `finished_status` tinyint NULL DEFAULT 2 COMMENT '完成状态1已完成2未完成',
                               `view_status` tinyint NULL DEFAULT 2 COMMENT '展示状态1展示2隐藏',
                               `number` int NOT NULL DEFAULT 0 COMMENT '测评次数',
                               `test_level` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '适用阶段（字典）',
                               `score_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '分数设置json',
                               `comment_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '评价设置规则json',
                               `created_by` int NULL DEFAULT 0 COMMENT '创建者',
                               `created_at` datetime NULL DEFAULT NULL,
                               `updated_at` datetime NULL DEFAULT NULL,
                               `deleted_at` datetime NULL DEFAULT NULL,
                               PRIMARY KEY (`test_id`) USING BTREE,
                               UNIQUE INDEX `test_id`(`test_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '试卷主表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for test_paper_know
-- ----------------------------
DROP TABLE IF EXISTS `test_paper_know`;
CREATE TABLE `test_paper_know`  (
                                    `test_id` int NOT NULL AUTO_INCREMENT,
                                    `test_code` char(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '试卷号（时间戳加4位随机）',
                                    `question_num` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '题目A+B',
                                    `paper_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '试卷名称',
                                    `data_json` json NULL COMMENT '试卷',
                                    `created_by` int UNSIGNED NULL DEFAULT 0,
                                    `created_at` datetime NULL DEFAULT NULL,
                                    `updated_at` datetime NULL DEFAULT NULL,
                                    `deleted_at` datetime NULL DEFAULT NULL,
                                    PRIMARY KEY (`test_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36864 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '试卷主表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
