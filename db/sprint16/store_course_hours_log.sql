update store_course_hours_log set timetable_id = 0 where timetable_id is null;
alter table store_course_hours_log
    modify timetable_id bigint default 0 not null;
alter table store_course_hours_log
    modify operation_type varchar(50) not null comment '操作类型: GIFT,TRIAL,ENROLL';


ALTER TABLE `store_course_hours_log`
    ADD COLUMN `total_amount` float(4) UNSIGNED NULL DEFAULT 0 COMMENT '总金额' AFTER `update_by`,
ADD COLUMN `unit_price` float(4) UNSIGNED NULL DEFAULT 0 COMMENT '单价' AFTER `total_amount`;


ALTER TABLE `store_course_hours_record`
    ADD COLUMN `total_amount` float(4) UNSIGNED NULL DEFAULT 0 COMMENT '总金额' AFTER `update_by`,
ADD COLUMN `unit_price` float(4) UNSIGNED NULL DEFAULT 0 COMMENT '单价' AFTER `total_amount`;