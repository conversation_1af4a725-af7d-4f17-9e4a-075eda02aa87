/*课程表*/
alter table course
    add course_type_id bigint null comment '课程类型关联id' after publish_time;

alter table course
    add charge_method int null comment '收费方式:0-门店设置;1-自定义' after course_type_id;

alter table course
    add customize_fee decimal(10, 4) null comment '自定义课时费标准(收费方式为自定义时设置)' after charge_method;

/*课程发布表*/
alter table course_pub
    add course_type_id bigint null comment '课程类型关联id' after publish_status;

alter table course_pub
    add charge_method int null comment '收费方式:0-门店设置;1-自定义' after course_type_id;

alter table course_pub
    add customize_fee decimal(10, 4) null comment '自定义课时费标准(收费方式为自定义时设置)' after charge_method;


create table ydsfx_teaching_dev.course_auth_store
(
    id          bigint auto_increment
        primary key,
    course_id   bigint                                 null comment '课程表id',
    store_id    bigint                                 null comment '门店id',
    create_by   varchar(255)                           null comment '创建人',
    create_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by   varchar(255) default ''                null comment '修改人',
    update_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间'
)
    comment '课程授权门店表';

create index course_auth_store_course_id_store_id_index
    on ydsfx_teaching_dev.course_auth_store (course_id, store_id);


create table ydsfx_teaching_dev.course_auth_store_his
(
    id          bigint auto_increment
        primary key,
    course_id   bigint                                     null comment '课程表id',
    store_id    bigint                                     null comment '门店id',
    auth_status int                                        not null comment '授权状态:0-正常授权;1-取消授权',
    del_flag    tinyint unsigned default '0'               not null comment '是否删除',
    create_by   varchar(255)                               null comment '创建人',
    create_time datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by   varchar(255)     default ''                null comment '修改人',
    update_time datetime         default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间'
)
    comment '课程授权门店历史表';

create index course_auth_store_course_id_store_id_index
    on ydsfx_teaching_dev.course_auth_store_his (course_id, store_id);
