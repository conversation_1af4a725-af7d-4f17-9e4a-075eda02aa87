CREATE TABLE `course_fee` (
                              `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '课消ID',
                              `del_flag` tinyint unsigned DEFAULT '0' COMMENT '是否删除: 0-正常;1-删除',
                              `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                              `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                              `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                              `standard_price` decimal(10,4) DEFAULT '0.0000' COMMENT '标准价格',
                              `effective_date` date DEFAULT NULL COMMENT '生效日期',
                              `store_id` bigint DEFAULT NULL COMMENT '门店ID',
                              `course_type_id` bigint DEFAULT NULL COMMENT '课程类型ID',
                              `course_id` bigint DEFAULT NULL COMMENT '课程ID',
                              `opt_type` int DEFAULT '0' COMMENT '操作类型: 0-门店设置; 1-自定义'
                              PRIMARY KEY (`id`)
)