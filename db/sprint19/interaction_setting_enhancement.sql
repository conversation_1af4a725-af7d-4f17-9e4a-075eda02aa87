-- 为ss_interaction_setting表添加互动页ID和题目信息字段
-- 用于记录标准化课件下答题或投票互动的页面信息和题目详情

-- 添加互动页ID字段
ALTER TABLE ss_interaction_setting 
ADD COLUMN page_id BIGINT NULL COMMENT '互动页ID' AFTER correct_answers;

-- 添加题目标题字段
ALTER TABLE ss_interaction_setting 
ADD COLUMN question_title VARCHAR(500) NULL COMMENT '题目标题' AFTER page_id;

-- 添加题目内容字段（JSON格式存储完整题目信息）
ALTER TABLE ss_interaction_setting 
ADD COLUMN question_content TEXT NULL COMMENT '题目内容(JSON格式,包含选项等详细信息)' AFTER question_title;

-- 为page_id字段添加索引，便于后期查询和排查问题
CREATE INDEX idx_page_id ON ss_interaction_setting (page_id);
