CREATE TABLE `store_course_export` (
                                       `export_id` int unsigned NOT NULL AUTO_INCREMENT,
                                       `type` tinyint unsigned DEFAULT '0' COMMENT '下载类型 1 课消明细',
                                       `param` json DEFAULT NULL COMMENT '条件',
                                       `status` tinyint unsigned DEFAULT '1' COMMENT '1 创建 2 进行中 3 导出失败  4 导出成功',
                                       `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '',
                                       `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                       `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                       `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                       `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                                       `store_id` int DEFAULT NULL COMMENT '门店id',
                                       `user_id` int NOT NULL COMMENT '用户id',
                                       `start_date` datetime DEFAULT NULL COMMENT '开始时间',
                                       `end_date` datetime DEFAULT NULL COMMENT '结束时间',
                                       PRIMARY KEY (`export_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;