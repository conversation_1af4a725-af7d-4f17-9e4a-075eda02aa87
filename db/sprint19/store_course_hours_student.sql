CREATE TABLE `store_course_hours_student`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `school_id`    bigint NOT NULL COMMENT '校区ID',
    `store_id`     bigint NOT NULL COMMENT '门店ID',
    `student_id`   bigint NOT NULL COMMENT '学生ID',
    `course_type`  int    NOT NULL COMMENT '课程类型',
    `course_hours` int                                                           DEFAULT '0' COMMENT '剩余课时',
    `formal`       int    NOT NULL                                               DEFAULT '0' COMMENT '正式课时',
    `gift`         int    NOT NULL                                               DEFAULT '0' COMMENT '赠送课时',
    `total_amount` decimal(10, 4) unsigned NOT NULL DEFAULT '0.0000' COMMENT '总金额',
    `del_flag`     tinyint                                                       DEFAULT '0' COMMENT '是否删除',
    `create_by`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    `create_time`  datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `update_time`  datetime                                                      DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uq_student_type` (`school_id`,`student_id`,`course_type`) USING BTREE,
    KEY            `idx_student_id` (`student_id`),
    KEY            `idx_store_id` (`store_id`,`student_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='学员课时类型剩余记录表';

-- 清空现有数据（可选）
TRUNCATE TABLE store_course_hours_student;

-- 插入新的汇总数据
INSERT INTO store_course_hours_student (school_id, store_id, student_id, course_type,
                                        formal, gift, course_hours, total_amount,
                                        del_flag, create_by, update_by, create_time, update_time)
SELECT r.school_id,
       r.store_id,
       r.student_id,
       r.course_type,
       SUM(CASE WHEN r.operation_type = 'ENROLL' THEN r.quantity ELSE 0 END) AS formal,
       SUM(CASE WHEN r.operation_type = 'GIFT' THEN r.quantity ELSE 0 END)   AS gift,
       SUM(r.quantity)                                                       AS course_hours, -- 假设 quantity 是剩余课时
       SUM(r.total_amount)                                                   AS total_amount,
       0                                                                     AS del_flag,     -- 默认未删除
       'system'                                                              AS create_by,    -- 固定创建人
       'system'                                                              AS update_by,    -- 固定更新人
       NOW()                                                                 AS create_time,
       NOW()                                                                 AS update_time
FROM store_course_hours_record r
WHERE r.del_flag = 0 -- 过滤未删除的数据
GROUP BY r.student_id, r.course_type, r.school_id, r.store_id;


CREATE TABLE `store_course_hours_temp`
(
    `id`          bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `batch_no`    bigint unsigned NOT NULL COMMENT '批次号,主要用于区分赠送与正式课时的关系,年月日+8位随机数组成',
    `status`      tinyint unsigned DEFAULT '0' COMMENT '默认0',
    `create_time` datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `del_flag`    tinyint                                                       DEFAULT '0' COMMENT '是否删除',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='课时操作记录表';


INSERT INTO store_course_hours_temp (batch_no, create_time, update_time)
SELECT s.batch_no,
       NOW(),
       NOW()
FROM store_course_hours_record s
         LEFT JOIN store_course_hours_temp t ON s.batch_no = t.batch_no
WHERE t.id IS NULL
  AND s.count > 0
  AND s.del_flag = 0
  AND s.quantity > 0
GROUP BY s.batch_no;


