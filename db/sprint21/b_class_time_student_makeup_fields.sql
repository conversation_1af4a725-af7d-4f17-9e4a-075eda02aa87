-- 为补课临加学员移除功能添加字段
ALTER TABLE `b_class_time_student` 
ADD COLUMN `source_lesson_no` bigint DEFAULT NULL COMMENT '来源课次编号（补课临加学员记录原课次）' AFTER `lesson_no`,
ADD COLUMN `is_manually_removed` tinyint DEFAULT 0 COMMENT '是否手动移除：0-否；1-是（用于补课中手动移除的原课次临加学员）' AFTER `adjust_status`;

-- 添加索引优化查询性能
CREATE INDEX `idx_source_lesson` ON `b_class_time_student`(`source_lesson_no`);
CREATE INDEX `idx_manually_removed` ON `b_class_time_student`(`is_manually_removed`);
CREATE INDEX `idx_lesson_source_student` ON `b_class_time_student`(`lesson_no`, `source_lesson_no`, `student_id`);
