-- 生成父字典ID
SELECT @dictParentId := FLOOR(RAND() * 1000000);

-- 插入字典类型 intent_level
INSERT INTO ydsfx.sys_dict (
    id, dict_type, description, create_by, update_by, create_time, update_time,
    remarks, system_flag, del_flag, tenant_id
) VALUES (
             @dictParentId, 'intent_level', '意向会员意愿等级', '0:1:管理员', ' ', '2025-06-25 10:41:02', NULL,
             NULL, '0', 0, 1
         );

-- 插入字典项 A
INSERT INTO ydsfx.sys_dict_item (
    id, dict_id, item_value, label, dict_type, description, sort_order,
    create_by, update_by, create_time, update_time, remarks, del_flag, tenant_id
) VALUES (
             FLOOR(RAND() * 1000000), @dictParentId, '1', 'A', 'intent_level', 'A', 1,
             '管理员', '0:1:管理员', '2025-06-25 10:41:15', '2025-06-25 10:42:08', 'A', 0, 1
         );

-- 插入字典项 B
INSERT INTO ydsfx.sys_dict_item (
    id, dict_id, item_value, label, dict_type, description, sort_order,
    create_by, update_by, create_time, update_time, remarks, del_flag, tenant_id
) VALUES (
             FLOOR(RAND() * 1000000), @dictParentId, '2', 'B', 'intent_level', 'B', 2,
             '管理员', '0:1:管理员', '2025-06-25 10:41:29', '2025-06-25 10:41:35', 'B', 0, 1
         );

-- 插入字典项 C
INSERT INTO ydsfx.sys_dict_item (
    id, dict_id, item_value, label, dict_type, description, sort_order,
    create_by, update_by, create_time, update_time, remarks, del_flag, tenant_id
) VALUES (
             FLOOR(RAND() * 1000000), @dictParentId, '3', 'C', 'intent_level', 'C', 3,
             '0:1:管理员', ' ', '2025-06-25 10:41:55', NULL, 'C', 0, 1
         );



-- 1. 获取 student_source 的 dict_id
SELECT id INTO @studentSourceDictParentId
FROM ydsfx.sys_dict
WHERE dict_type = 'student_source' AND del_flag = 0
    LIMIT 1;

-- 插入 分享邀请
INSERT INTO ydsfx.sys_dict_item (
    id, dict_id, item_value, label, dict_type, description, sort_order,
    create_by, update_by, create_time, update_time, remarks, del_flag, tenant_id
) VALUES (
             FLOOR(RAND() * 1000000), @studentSourceDictParentId, '4', '分享邀请', 'student_source', '分享邀请', 4,
             '0:1:管理员', ' ', NOW(), NULL, NULL, 0, 1
         );

-- 插入 测评录入
INSERT INTO ydsfx.sys_dict_item (
    id, dict_id, item_value, label, dict_type, description, sort_order,
    create_by, update_by, create_time, update_time, remarks, del_flag, tenant_id
) VALUES (
             FLOOR(RAND() * 1000000), @studentSourceDictParentId, '5', '测评录入', 'student_source', '测评录入', 5,
             '0:1:管理员', ' ', NOW(), NULL, NULL, 0, 1
         );


/*会员跟踪记录表*/
create table ss_course.store_student_track_record
(
    id                    bigint auto_increment comment '主键ID'
        primary key,
    school_id             int                                        null comment '学校id',
    store_id              int                                        null comment '门店ID',
    user_id               int unsigned                               not null comment '会员id，store_student中的user_id',
    willingness_level     int                                        not null comment '意愿等级',
    communication_records varchar(500)                               null comment '沟通记录',
    recommend_teacher     int                                        null comment '推荐老师',
    recommend_student     int                                        null comment '推荐学员',
    create_by             varchar(255)                               null comment '创建人',
    create_time           datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by             varchar(255)                               null comment '修改人',
    update_time           datetime         default CURRENT_TIMESTAMP not null comment '修改时间',
    del_flag              tinyint unsigned default '0'               not null comment '是否删除: 0-否; 1-是;'
)
    comment '会员跟踪记录';

/*默认意向会员等级为1即为A*/
alter table store_student_track_record
    alter column willingness_level set default 1;



