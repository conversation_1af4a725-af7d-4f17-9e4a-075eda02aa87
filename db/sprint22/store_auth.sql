CREATE TABLE `store_role`
(
    `role_id`     bigint                                                       NOT NULL COMMENT '角色ID',
    `role_name`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '角色名称',
    `role_code`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '角色编码',
    `role_desc`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT NULL COMMENT '角色描述',
    `ds_type`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci              DEFAULT '2' COMMENT '数据权限类型，0全部，1自定义，2本部门及以下，3本部门，4仅本人',
    `ds_scope`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT NULL COMMENT '数据权限范围',
    `create_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT ' ' COMMENT '创建人',
    `update_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT ' ' COMMENT '修改人',
    `create_time` datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime                                                              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag`    tinyint(1) DEFAULT '0' COMMENT '删除标记，0未删除，1已删除',
    `tenant_id`   bigint                                                                DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`role_id`),
    KEY           `role_idx1_role_code` (`role_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='门店角色表';


CREATE TABLE `store_role_menu`
(
    `role_id` bigint NOT NULL COMMENT '角色ID',
    `menu_id` bigint NOT NULL COMMENT '菜单ID',
    PRIMARY KEY (`role_id`, `menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='门店角色菜单表';


CREATE TABLE `store_menu`
(
    `menu_id`     bigint                                                       NOT NULL COMMENT '菜单ID',
    `name`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '菜单名称',
    `permission`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT NULL COMMENT '权限标识',
    `path`        varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT NULL COMMENT '路由路径',
    `component`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT NULL COMMENT '组件',
    `parent_id`   bigint                                                                DEFAULT NULL COMMENT '父菜单ID',
    `icon`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '菜单图标',
    `visible`     char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci              DEFAULT '1' COMMENT '是否可见，0隐藏，1显示',
    `sort_order`  int                                                                   DEFAULT '1' COMMENT '排序值，越小越靠前',
    `keep_alive`  char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci              DEFAULT '0' COMMENT '是否缓存，0否，1是',
    `embedded`    char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci              DEFAULT NULL COMMENT '是否内嵌，0否，1是',
    `menu_type`   char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci              DEFAULT '0' COMMENT '菜单类型，0目录，1菜单，2按钮',
    `create_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT ' ' COMMENT '创建人',
    `create_time` datetime                                                              DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT ' ' COMMENT '修改人',
    `update_time` datetime                                                              DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag`    tinyint(1) DEFAULT '0' COMMENT '删除标记，0未删除，1已删除',
    `tenant_id`   bigint unsigned DEFAULT NULL COMMENT '租户ID',
    PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='门店菜单权限表';