create table ss_course_dev.wx_student_msg
(
    id               bigint                               not null comment '主键'
        primary key,
    app_name         varchar(50)                          null comment '公众号名称',
    school_id        bigint unsigned                      null comment '校区ID',
    store_id         bigint                               null comment '门店ID',
    student_id       bigint                               not null comment '学生ID',
    obj_id           bigint                               null comment '对象ID（如：课次编号 lessonNo）',
    obj_type         tinyint                              not null comment '对象类型（如：同课程类型）',
    class_start_time datetime                             not null comment '开始上课时间',
    type             int                                  null comment '消息分类（1、用户发给公众号；2、公众号发给用户；）',
    rep_type         char(20)                             null comment '消息类型（text：文本；image：图片；voice：语音；video：视频；shortvideo：小视频；location：地理位置；music：音乐；news：图文；event：推送事件）',
    rep_event        int                                  null comment '事件类型（1、上课提醒；2、课消提醒；）',
    rep_content      text                                 null comment '回复类型文本保存文字、地理位置信息',
    rep_media_id     varchar(64)                          null comment '回复类型imge、voice、news、video的mediaID或音乐缩略图的媒体id',
    rep_name         varchar(100)                         null comment '回复的素材名、视频和音乐的标题',
    rep_desc         varchar(200)                         null comment '视频和音乐的描述',
    rep_url          varchar(500)                         null comment '链接',
    content          json                                 null comment '图文消息的内容',
    send_status      tinyint    default 0                 not null comment '消息发送状态（0：未发送；1：已发送；2：发送失败；）',
    read_flag        tinyint(1) default 0                 null comment '已读标记（1：是；0：否）',
    app_id           varchar(100)                         null comment '公众号ID',
    open_id          varchar(100)                         null comment '微信唯一标识',
    remark           varchar(100) charset utf8mb3         null comment '备注',
    del_flag         tinyint(1) default 0                 null comment '删除标记，0未删除，1已删除',
    create_time      datetime   default CURRENT_TIMESTAMP null comment '创建时间',
    update_time      datetime   default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint wx_student_msg_pk
        unique (student_id, obj_id, obj_type, rep_event)
)
    comment '学生家长的微信消息' row_format = DYNAMIC;

create table store_setting
(
    id            bigint auto_increment
        primary key,
    store_id      bigint                                     not null comment '门店ID',
    setting_key   varchar(64)                                not null comment '设置项key',
    setting_value varchar(255)                               not null comment '设置项value',
    remark        varchar(255)                               null comment '备注',
    create_by     varchar(255)                               null comment '创建人',
    create_time   datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by     varchar(255)                               null comment '修改人',
    update_time   datetime         default CURRENT_TIMESTAMP not null comment '修改时间',
    del_flag      tinyint unsigned default '0'               not null comment '是否删除: 0-否; 1-是;',
    constraint store_id_setting_key_index
        unique (store_id, setting_key)
)
    comment '门店属性设置表';

