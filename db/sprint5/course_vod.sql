-- CourseVod Controller 按钮权限
INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '新增', 'edusystem_CourseVod_add', NULL, NULL, 1873173023326515202, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '编辑', 'edusystem_CourseVod_edit', NULL, NULL, 1873173023326515202, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '删除', 'edusystem_CourseVod_del', NULL, NULL, 1873173023326515202, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '查看', 'edusystem_CourseVod_view', NULL, NULL, 1873173023326515202, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);


-- ClassTime Controller 按钮权限
INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '查看', 'edusystem_ClassTime_view', NULL, NULL, 1873169017958371329, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);