CREATE TABLE `ea_recording`  (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `device_id` bigint NOT NULL COMMENT '录课设备ID',
                                 `record_video_task_id` bigint NOT NULL COMMENT '录课任务ID',
                                 `audit_status` tinyint NULL DEFAULT 0 COMMENT '提交状态: 0-未提交; 1-待审核; 2-已通过; 3-未通过;',
                                 `agora_record_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '声网录制ID',
                                 `room_uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '声网房间UUID',
                                 `recording_status` tinyint NULL DEFAULT 0 COMMENT '录制状态: 0-待录制; 1-录制中; 2-正常录制完成; 3-录制作废(重新录制); 4-视频处理中; 5-视频处理失败; 6-停止录制; 7-转码中; 8-转码失败;',
                                 `recording_resources` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '录制资源',
                                 `vod_video_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '视频点播Vod中videoId',
                                 `agora_cloud_record_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '声网云端录制ID',
                                 `cloud_recording_resources` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '云端录制资源地址',
                                 `agora_cloud_record_individual_resource_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '声网单流云录制ID',
                                 `agora_cloud_record_individual_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '声网云端录制单流ID',
                                 `download_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下载地址',
                                 `download_vod_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '下载Vod的Id',
                                 `recording_time` datetime NULL DEFAULT NULL COMMENT '录制时间(yyyy-MM-dd HH:mm:ss)',
                                 `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                 `del_flag` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除: 0-否; 1-是;',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 INDEX `idx_room_uuid`(`room_uuid`) USING BTREE,
                                 INDEX `idx_agora_cloud_record_id`(`agora_cloud_record_id`) USING BTREE,
                                 INDEX `idx_record_video_task_id`(`record_video_task_id`) USING BTREE,
                                 INDEX `idx_device_id`(`device_id`) USING BTREE,
                                 INDEX `idx_recording_status`(`recording_status`) USING BTREE,
                                 INDEX `idx_audit_status`(`audit_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '主讲录课表' ROW_FORMAT = Dynamic;