-- LiveRoomPlanDraft Controller 按钮权限
INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '新增', 'edusystem_LiveRoomPlanDraft_add', NULL, NULL, 1873168403430682625, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '编辑', 'edusystem_LiveRoomPlanDraft_edit', NULL, NULL, 1873168403430682625, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '删除', 'edusystem_LiveRoomPlanDraft_del', NULL, NULL, 1873168403430682625, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '发布', 'edusystem_LiveRoomPlanDraft_publish', NULL, NULL, 1873168403430682625, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '查询', 'edusystem_LiveRoomPlanDraft_view', NULL, NULL, 1873168403430682625, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '详情', 'edusystem_LiveRoomPlanDraft_detail', NULL, NULL, 1873168403430682625, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

-- LiveRoomPlanDetailDraft Controller 按钮权限
INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '新增', 'edusystem_LiveRoomPlanDetailDraft_add', NULL, NULL, 1873171258665381890, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '编辑', 'edusystem_LiveRoomPlanDetailDraft_edit', NULL, NULL, 1873171258665381890, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '删除', 'edusystem_LiveRoomPlanDetailDraft_del', NULL, NULL, 1873171258665381890, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

-- TeachingPlanDraft Controller 按钮权限
INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '新增', 'edusystem_TeachingPlanDraft_add', NULL, NULL, 1873174577164390401, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '编辑', 'edusystem_TeachingPlanDraft_edit', NULL, NULL, 1873174577164390401, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '删除', 'edusystem_TeachingPlanDraft_del', NULL, NULL, 1873174577164390401, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '发布', 'edusystem_TeachingPlanDraft_publish', NULL, NULL, 1873174577164390401, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '查询', 'admin_TeachingPlanDraft_view', NULL, NULL, 1873174577164390401, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '详情', 'admin_TeachingPlanDraft_detail', NULL, NULL, 1873174577164390401, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);

-- TeachingPlanDetailDraft Controller 按钮权限
INSERT INTO sys_menu (`menu_id`, `name`, `permission`, `path`, `component`, `parent_id`, `icon`, `visible`, `sort_order`, `keep_alive`, `embedded`, `menu_type`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `tenant_id`) VALUES
    (FLOOR(RAND() * 1000000), '编辑', 'edusystem_TeachingPlanDetailDraft_edit', NULL, NULL, 1873174885786234882, NULL, '0', 0, '0', '0', '1', 'admin', sysdate(), 'admin', sysdate(), '0', 1);