CREATE TABLE `ea_class_time` (
                                 `id` bigint NOT NULL COMMENT '主键id',
                                 `name` varchar(50) NOT NULL COMMENT '时段名称',
                                 `type` tinyint NOT NULL COMMENT '时段类型:1-上午;2-下午;3-晚上',
                                 `start_time` time NOT NULL COMMENT '开始时间 (HH:mm:ss)',
                                 `end_time` time NOT NULL COMMENT '结束时间 (HH:mm:ss)',
                                 `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                 `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='上课时段表';

insert into ea_class_time (id, name, type, start_time, end_time, create_by, create_time, update_by, update_time, del_flag)
values
    (1000,'上午8:00',1,'08:00:00','10:00:00','0:1:管理员',now(),'0:1:管理员',now(),0),
    (2000,'上午9:00',1,'09:00:00','11:00:00','0:1:管理员',now(),'0:1:管理员',now(),0),
    (3000,'上午10:30',1,'10:30:00','12:30:00','0:1:管理员',now(),'0:1:管理员',now(),0),
    (4000,'下午13:30',2,'13:30:00','15:30:00','0:1:管理员',now(),'0:1:管理员',now(),0),
    (5000,'下午14:00',2,'14:00:00','16:00:00','0:1:管理员',now(),'0:1:管理员',now(),0),
    (6000,'下午16:00',2,'16:00:00','18:00:00','0:1:管理员',now(),'0:1:管理员',now(),0),
    (7000,'晚上18:20',3,'18:20:00','20:20:00','0:1:管理员',now(),'0:1:管理员',now(),0),
    (8000,'晚上18:30',3,'18:30:00','20:30:00','0:1:管理员',now(),'0:1:管理员',now(),0);

CREATE TABLE `ea_course_vod` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                 `stage` int NOT NULL COMMENT '阶段',
                                 `course_id` bigint NOT NULL COMMENT '课程ID',
                                 `lesson_id` bigint DEFAULT NULL COMMENT '课节ID',
                                 `lecture_id` bigint NOT NULL COMMENT '主讲老师ID',
                                 `disable` tinyint NOT NULL DEFAULT '0' COMMENT '是否停用(0未停用，1停用)',
                                 `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                 PRIMARY KEY (`id`),
                                 KEY `ea_course_id_lesson_id_lecture_id_index` (`course_id`,`lesson_id`,`lecture_id`),
                                 KEY `ea_course_vod_stage_index` (`stage`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='点播课程库';



CREATE TABLE `ea_course_vod_video` (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                       `course_vod_id` bigint NOT NULL COMMENT '点播课ID',
                                       `aliyun_vod_id` varchar(255) NOT NULL COMMENT '阿里云视频点播ID',
                                       `aliyun_play_url` varchar(255) NOT NULL COMMENT '阿里云视频播放地址',
                                       `mp4_url` varchar(255) NOT NULL COMMENT 'mp4视频地址',
                                       `record_video_task_id` bigint DEFAULT NULL COMMENT '录制任务ID',
                                       `recording_id` bigint DEFAULT NULL COMMENT '主讲录课ID',
                                       `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_by` varchar(255) DEFAULT NULL COMMENT '更新人',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                       `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                       PRIMARY KEY (`id`),
                                       KEY `ea_course_vod_id_index` (`course_vod_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='点播课程视频';



CREATE TABLE `ea_live_channel` (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `teaching_plan_detail_id` bigint DEFAULT NULL COMMENT '教学计划排期ID',
                                   `channel_id` varchar(255) DEFAULT NULL COMMENT '频道ID',
                                   `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                   `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除: 0-未删除;1-已删除',
                                   PRIMARY KEY (`id`),
                                   KEY `ea_teaching_plan_index` (`teaching_plan_detail_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='直播频道';

CREATE TABLE `ea_live_room_plan_detail_draft` (
                                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                  `plan_id` bigint DEFAULT NULL COMMENT '直播间计划id，ea_live_room_plan_draft.id',
                                                  `time_slot_id` bigint DEFAULT NULL COMMENT '上课时段ID',
                                                  `class_date` date DEFAULT NULL COMMENT '上课开始日期（yyyy-MM-dd）',
                                                  `class_start_time` time DEFAULT NULL COMMENT '上课开始时间（HH:mm:ss）',
                                                  `class_end_time` time DEFAULT NULL COMMENT '上课结束时间（HH:mm:ss）',
                                                  `class_start_date_time` datetime DEFAULT NULL COMMENT '上课开始日期时间（yyyy-MM-dd HH:mm:ss)',
                                                  `class_end_date_time` datetime DEFAULT NULL COMMENT '上课结束日期时间（yyyy-MM-dd HH:mm:ss)',
                                                  `detail_type` int DEFAULT NULL COMMENT '明细类型:0-单节课;1-常规课;2-精品课',
                                                  `lesson_order` int DEFAULT NULL COMMENT '第几节课',
                                                  `operator_name` varchar(255) NOT NULL COMMENT '操作人',
                                                  `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                                  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                  `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                                  PRIMARY KEY (`id`),
                                                  KEY `idx_plan_id` (`plan_id`),
                                                  KEY `idx_lesson_order` (`lesson_order`),
                                                  KEY `idx_class_time` (`class_start_time`,`class_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='直播间计划明细草稿表';

CREATE TABLE `ea_live_room_plan_detail_version` (
                                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                    `plan_id` bigint DEFAULT NULL COMMENT '直播间计划id',
                                                    `time_slot_id` bigint DEFAULT NULL COMMENT '上课时段ID',
                                                    `class_date` date DEFAULT NULL COMMENT '上课开始日期（yyyy-MM-dd）',
                                                    `class_start_time` time DEFAULT NULL COMMENT '上课开始时间（HH:mm:ss）',
                                                    `class_end_time` time DEFAULT NULL COMMENT '上课结束时间（HH:mm:ss）',
                                                    `class_start_date_time` datetime DEFAULT NULL COMMENT '上课开始日期时间（yyyy-MM-dd HH:mm:ss)',
                                                    `class_end_date_time` datetime DEFAULT NULL COMMENT '上课结束日期时间（yyyy-MM-dd HH:mm:ss)',
                                                    `detail_type` int DEFAULT NULL COMMENT '明细类型:0-单节课;1-常规课;2-精品课',
                                                    `lesson_order` int DEFAULT NULL COMMENT '第几节课',
                                                    `version` int NOT NULL DEFAULT '0' COMMENT '版本号',
                                                    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                    `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                                    PRIMARY KEY (`id`),
                                                    KEY `ea_plan_id_index` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='直播间计划明细';



CREATE TABLE `ea_live_room_plan_draft` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                           `plan_name` varchar(255) DEFAULT NULL COMMENT '计划名称',
                                           `stage` int DEFAULT NULL COMMENT '阶段',
                                           `live_room_id` bigint DEFAULT NULL COMMENT '草稿直播间',
                                           `plan_status` tinyint DEFAULT '0' COMMENT '直播间计划状态:0-未发布;1-已发布;',
                                           `publisher_name` varchar(255) DEFAULT NULL COMMENT '发布人姓名',
                                           `create_by` varchar(255) DEFAULT NULL COMMENT '草稿创建人',
                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '草稿创建时间',
                                           `update_by` varchar(255) DEFAULT NULL COMMENT '草稿修改人',
                                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '草稿修改时间',
                                           `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_plan_status` (`plan_status`),
                                           KEY `idx_live_room_id` (`live_room_id`),
                                           KEY `idx_stage` (`stage`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='直播间计划草稿表';

CREATE TABLE `ea_live_room_plan_version` (
                                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id，ea_live_room_plan_draft.id',
                                             `plan_id` bigint NOT NULL COMMENT '直播间计划ID',
                                             `plan_name` varchar(255) DEFAULT NULL COMMENT '计划名称',
                                             `stage` int DEFAULT NULL COMMENT '阶段',
                                             `live_room_id` bigint DEFAULT NULL COMMENT '直播间id',
                                             `version` int DEFAULT '0' COMMENT '版本号',
                                             `online_version` tinyint NOT NULL DEFAULT '0' COMMENT '线上使用版本:1线上使用版本，0历史版本',
                                             `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                             `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                             `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                             PRIMARY KEY (`id`),
                                             KEY `idx_plan_id_version` (`plan_id`,`version`),
                                             KEY `idx_online_version` (`online_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='直播间计划版本记录表';

CREATE TABLE `ea_live_room_plan_version_no` (
                                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '版本号ID',
                                                `plan_id` bigint NOT NULL COMMENT '直播间计划ID',
                                                `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                                PRIMARY KEY (`id`),
                                                KEY `ea_plan_id_index` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='直播间计划版本生成记录表';



CREATE TABLE `ea_record_video_task` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                        `teaching_plan_id` bigint DEFAULT NULL COMMENT '教学计划ID',
                                        `teaching_plan_detail_id` bigint DEFAULT NULL COMMENT '教学计划排期ID',
                                        `lesson_order` int DEFAULT NULL COMMENT '第几节课',
                                        `courseware_id` bigint DEFAULT NULL COMMENT '课件ID',
                                        `courseware_version` int DEFAULT NULL COMMENT '课件版本',
                                        `course_id` bigint DEFAULT NULL COMMENT '课程id',
                                        `course_version` int DEFAULT NULL COMMENT '课程版本号',
                                        `lesson_id` bigint DEFAULT NULL COMMENT '课节id',
                                        `lesson_version` int DEFAULT NULL COMMENT '课件版本号',
                                        `earliest_start_date` datetime DEFAULT NULL COMMENT '最早开始日期',
                                        `lecture_id` bigint DEFAULT NULL COMMENT '主讲老师id',
                                        `lecture_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主讲名字',
                                        `task_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '任务状态:0-未完成;1-已完成;3-已取消;4-录制中;',
                                        `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                        `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                        PRIMARY KEY (`id`),
                                        KEY `ea_lecture_id_earliest_start_date_index` (`lecture_id`,`earliest_start_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='录课任务表';



CREATE TABLE `ea_recording` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                `device_id` bigint NOT NULL COMMENT '录课设备ID',
                                `record_video_task_id` bigint NOT NULL COMMENT '录课任务ID',
                                `audit_status` tinyint DEFAULT '0' COMMENT '提交状态: 0-未提交; 1-待审核; 2-已通过; 3-未通过;',
                                `agora_record_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '声网录制ID',
                                `room_uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '声网房间UUID',
                                `recording_status` tinyint DEFAULT '0' COMMENT '录制状态: 0-待录制; 1-录制中; 2-正常录制完成; 3-录制作废(重新录制); 4-视频处理中; 5-视频处理失败; 6-停止录制; 7-转码中; 8-转码失败;',
                                `recording_resources` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '录制资源',
                                `vod_video_id` varchar(255) DEFAULT NULL COMMENT '视频点播Vod中videoId',
                                `agora_cloud_record_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '声网云端录制ID',
                                `cloud_recording_resources` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '云端录制资源地址',
                                `agora_cloud_record_individual_resource_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '声网单流云录制ID',
                                `agora_cloud_record_individual_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '声网云端录制单流ID',
                                `download_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下载地址',
                                `download_vod_id` varchar(255) DEFAULT NULL COMMENT '下载Vod的Id',
                                `recording_time` datetime DEFAULT NULL COMMENT '录制时间(yyyy-MM-dd HH:mm:ss)',
                                `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                                PRIMARY KEY (`id`),
                                KEY `idx_agora_cloud_record_id` (`agora_cloud_record_id`),
                                KEY `idx_record_video_task_id` (`record_video_task_id`),
                                KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='主讲录课表';



CREATE TABLE `ea_teaching_plan_detail_draft` (
                                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                 `plan_id` bigint DEFAULT NULL COMMENT '教学计划id',
                                                 `lecture_id` bigint DEFAULT NULL COMMENT '草稿主讲id',
                                                 `lesson_order` int DEFAULT NULL COMMENT '第几节课',
                                                 `time_slot_id` bigint DEFAULT NULL COMMENT '上课时段ID',
                                                 `lecture_name` varchar(255) DEFAULT NULL COMMENT '主讲名字',
                                                 `edit_remark` varchar(500) DEFAULT NULL COMMENT '修改备注',
                                                 `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                 `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                                 PRIMARY KEY (`id`),
                                                 KEY `idx_plan_id` (`plan_id`),
                                                 KEY `idx_lesson_order` (`lesson_order`),
                                                 KEY `ea_plan_id_index` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='教学计划明细草稿表';



CREATE TABLE `ea_teaching_plan_detail_pub` (
                                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                               `plan_id` bigint DEFAULT NULL COMMENT '教学计划id',
                                               `lesson_order` int DEFAULT NULL COMMENT '第几节课',
                                               `course_id` bigint DEFAULT NULL COMMENT '课程id',
                                               `course_name` varchar(255) DEFAULT NULL COMMENT '课程名字',
                                               `lesson_id` bigint DEFAULT NULL COMMENT '课节id',
                                               `lesson_name` varchar(255) DEFAULT NULL COMMENT '课节名字',
                                               `lecture_id` bigint DEFAULT NULL COMMENT '主讲id',
                                               `lecture_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '主讲名字',
                                               `book_id` bigint DEFAULT NULL COMMENT '书籍ID',
                                               `book_name` varchar(255) DEFAULT NULL COMMENT '书籍名称',
                                               `class_date` date DEFAULT NULL COMMENT '上课开始日期',
                                               `class_start_time` time DEFAULT NULL COMMENT '上课开始时间',
                                               `class_end_time` time DEFAULT NULL COMMENT '上课结束时间',
                                               `class_start_date_time` datetime DEFAULT NULL COMMENT '上课开始日期时间',
                                               `class_end_date_time` datetime DEFAULT NULL COMMENT '上课结束日期时间',
                                               `live_room_id` bigint NOT NULL DEFAULT '0' COMMENT '直播间ID',
                                               `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                               `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                               `time_slot_id` bigint DEFAULT NULL COMMENT '上课时段ID',
                                               PRIMARY KEY (`id`),
                                               KEY `ea_plan_id_index` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='已发布的教学计划明细表';



CREATE TABLE `ea_teaching_plan_detail_version` (
                                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                                   `plan_id` bigint DEFAULT NULL COMMENT '教学计划id',
                                                   `period` int DEFAULT NULL COMMENT '第几节课',
                                                   `course_id` bigint DEFAULT NULL COMMENT '课程id',
                                                   `course_name` varchar(255) DEFAULT NULL COMMENT '课程名字',
                                                   `lesson_id` bigint DEFAULT NULL COMMENT '课节id',
                                                   `lesson_name` varchar(255) DEFAULT NULL COMMENT '课节名字',
                                                   `book_id` bigint DEFAULT NULL COMMENT '书籍ID',
                                                   `book_name` varchar(255) DEFAULT NULL COMMENT '书籍名称',
                                                   `class_date` date DEFAULT NULL COMMENT '上课开始日期',
                                                   `class_start_time` time DEFAULT NULL COMMENT '上课开始时间',
                                                   `class_end_time` time DEFAULT NULL COMMENT '上课结束时间',
                                                   `class_start_date_time` datetime DEFAULT NULL COMMENT '上课开始日期时间',
                                                   `class_end_date_time` datetime DEFAULT NULL COMMENT '上课结束日期时间',
                                                   `lecture_id` bigint DEFAULT NULL COMMENT '主讲id',
                                                   `lecture_name` varchar(255) DEFAULT NULL COMMENT '主讲名字',
                                                   `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                   `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                                   `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                                   `time_slot_id` bigint DEFAULT NULL COMMENT '上课时段ID',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='教学计划明细版本发布记录表';

CREATE TABLE `ea_teaching_plan_draft` (
                                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                          `live_room_plan_id` bigint DEFAULT NULL COMMENT '直播间计划id',
                                          `plan_status` tinyint DEFAULT '0' COMMENT '发布状态:0-未发布;1-已发布;2-已关闭',
                                          `course_id` bigint DEFAULT NULL COMMENT '课程id',
                                          `course_name` varchar(255) DEFAULT NULL COMMENT '课程名字',
                                          `lecture_id` bigint DEFAULT NULL COMMENT '主讲老师id',
                                          `lecture_name` varchar(255) DEFAULT NULL COMMENT '主讲老师名字',
                                          `stage` int DEFAULT NULL COMMENT '阶段',
                                          `edit_remark` varchar(500) DEFAULT NULL COMMENT '修改备注',
                                          `create_by` varchar(255) DEFAULT NULL COMMENT '草稿创建人',
                                          `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '草稿创建时间',
                                          `update_by` varchar(255) DEFAULT NULL COMMENT '草稿修改人',
                                          `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '草稿修改时间',
                                          `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_live_room_plan_id` (`live_room_plan_id`),
                                          KEY `idx_course_id` (`course_id`),
                                          KEY `idx_lecture_id` (`lecture_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='教学计划草稿表';

CREATE TABLE `ea_teaching_plan_pub` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id，teaching_plan_draft.id',
                                        `teaching_plan_id` bigint DEFAULT NULL COMMENT '教学计划id',
                                        `live_room_plan_id` bigint DEFAULT NULL COMMENT '直播间计划id',
                                        `course_id` bigint DEFAULT NULL COMMENT '课程包id',
                                        `course_name` varchar(255) DEFAULT NULL COMMENT '课程包名字',
                                        `lecture_id` bigint DEFAULT NULL COMMENT '主讲id',
                                        `lecture_name` varchar(255) DEFAULT NULL COMMENT '主讲名字',
                                        `stage` int DEFAULT NULL COMMENT '阶段',
                                        `closed` tinyint NOT NULL DEFAULT '0' COMMENT '是否关闭',
                                        `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                        `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                        PRIMARY KEY (`id`),
                                        KEY `ea_teaching_plan_id_index` (`teaching_plan_id`),
                                        KEY `ea_stage_lecture_id_index` (`stage`,`lecture_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='已发布的教学计划表';



CREATE TABLE `ea_teaching_plan_version` (
                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                            `teaching_plan_id` bigint DEFAULT NULL COMMENT '教学计划id',
                                            `live_room_plan_id` bigint DEFAULT NULL COMMENT '直播间计划id',
                                            `course_id` bigint DEFAULT NULL COMMENT '课程包id',
                                            `course_name` varchar(255) DEFAULT NULL COMMENT '课程包名字',
                                            `lecture_id` bigint DEFAULT NULL COMMENT '主讲id',
                                            `lecture_name` varchar(255) DEFAULT NULL COMMENT '主讲名字',
                                            `stage` int DEFAULT NULL COMMENT '阶段',
                                            `version` int unsigned NOT NULL DEFAULT '0' COMMENT '版本号',
                                            `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                            `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_plan_id_version` (`teaching_plan_id`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='教学计划发布版本记录表';

CREATE TABLE `ea_teaching_plan_version_no` (
                                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '版本号ID',
                                               `plan_id` bigint NOT NULL COMMENT '教学计划ID',
                                               `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                               `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否;1-是',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='教学计划版本生成记录表';

CREATE TABLE `b_course_live` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `store_id` bigint NOT NULL COMMENT '门店ID',
                                 `teaching_plan_id` bigint NOT NULL COMMENT '教学计划ID',
                                 `time_slot_id` bigint NOT NULL COMMENT '上课时段ID',
                                 `stage` int DEFAULT NULL COMMENT '阶段',
                                 `class_id` bigint DEFAULT NULL COMMENT '班级ID',
                                 `classroom_id` bigint DEFAULT NULL COMMENT '教室ID',
                                 `teacher_id` bigint DEFAULT NULL COMMENT '指导老师ID',
                                 `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                 `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                                 PRIMARY KEY (`id`),
                                 KEY `b_store_id_time_slot_id_index` (`store_id`,`time_slot_id`),
                                 KEY `b_teaching_plan_id_index` (`teaching_plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店已约直播课';



CREATE TABLE `b_course_make_up` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                    `store_id` bigint NOT NULL COMMENT '门店ID',
                                    `timetable_id` bigint DEFAULT NULL COMMENT '已约直播课/点播课课表ID',
                                    `course_id` bigint DEFAULT NULL COMMENT '课程ID',
                                    `lesson_id` bigint DEFAULT NULL COMMENT '课节ID(废弃)',
                                    `lesson_order` int DEFAULT NULL COMMENT '第几节课',
                                    `time_slot_id` bigint DEFAULT NULL COMMENT '上课时段ID',
                                    `lecture_id` bigint NOT NULL COMMENT '主讲老师ID',
                                    `class_room_id` bigint DEFAULT NULL COMMENT '上课教室ID',
                                    `class_id` bigint DEFAULT NULL COMMENT '班级ID',
                                    `class_date` date DEFAULT NULL COMMENT '上课日期',
                                    `class_start_time` time DEFAULT NULL COMMENT '上课开始时间',
                                    `class_end_time` time DEFAULT NULL COMMENT '上课结束时间',
                                    `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                    `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                                    PRIMARY KEY (`id`),
                                    KEY `b_class_date_lecture_id_store_id_index` (`class_date`,`lecture_id`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店补课表';



CREATE TABLE `b_course_vod` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                `store_id` bigint NOT NULL COMMENT '门店ID',
                                `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '排课名称',
                                `course_id` bigint NOT NULL COMMENT '课程ID',
                                `stage` int DEFAULT NULL COMMENT '阶段',
                                `lecture_id` bigint DEFAULT NULL COMMENT '主讲老师ID',
                                `class_id` bigint DEFAULT NULL COMMENT '班级ID',
                                `classroom_id` bigint DEFAULT NULL COMMENT '教室ID',
                                `teacher_id` bigint DEFAULT NULL COMMENT '指导老师ID',
                                `scheduled` tinyint DEFAULT '0' COMMENT '是否已排课: 0-未排课; 1-已排课;',
                                `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                                PRIMARY KEY (`id`),
                                KEY `b_course_vod_store_id_index` (`store_id`),
                                KEY `b_course_vod_lecture_id_index` (`lecture_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店已约点播课';

CREATE TABLE `b_course_vod_plan` (
                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                     `store_id` bigint NOT NULL DEFAULT '0' COMMENT '门店ID',
                                     `vod_course_id` bigint NOT NULL COMMENT '门店已约点播课ID',
                                     `course_id` bigint DEFAULT NULL COMMENT '课程ID',
                                     `lesson_id` bigint DEFAULT NULL COMMENT '课节ID(废弃)',
                                     `time_slot_id` bigint DEFAULT NULL COMMENT '上课时段ID',
                                     `lesson_order` int DEFAULT NULL COMMENT '第几节课',
                                     `class_date` date DEFAULT NULL COMMENT '上课日期',
                                     `class_start_time` time DEFAULT NULL COMMENT '上课开始时间',
                                     `class_end_time` time DEFAULT NULL COMMENT '上课结束时间',
                                     `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
                                     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
                                     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                                     `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                                     PRIMARY KEY (`id`),
                                     KEY `b_course_vod_plan_vod_course_id_index` (`vod_course_id`),
                                     KEY `b_course_vod_plan_store_id_class_date_index` (`store_id`,`class_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店已约点播课排期表';

CREATE TABLE `b_timetable` (
                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                               `store_id` bigint NOT NULL COMMENT '门店ID',
                               `lesson_no` bigint NOT NULL COMMENT '这节课的编号，课程类型（1位）+ 第几节课（3位）+ 主表的ID',
                               `course_plan_id` bigint NOT NULL DEFAULT '0' COMMENT '门店已约直播课ID、门店已约点播课排期表ID、门店补课表ID',
                               `course_type` tinyint NOT NULL COMMENT '课程类型: 1-直播课; 2-点播课; 3-补课;',
                               `class_id` bigint DEFAULT NULL COMMENT '班级ID',
                               `classroom_id` bigint DEFAULT NULL COMMENT '教室ID',
                               `lecture_id` bigint DEFAULT NULL COMMENT '主讲老师ID',
                               `teacher_id` bigint NOT NULL DEFAULT '0' COMMENT '指导老师ID',
                               `course_id` bigint DEFAULT NULL COMMENT '课程ID',
                               `lesson_id` bigint DEFAULT NULL COMMENT '课节ID(废弃)',
                               `lesson_order` int DEFAULT NULL COMMENT '第几节课',
                               `time_slot_id` bigint DEFAULT NULL COMMENT '上课时段ID',
                               `time_slot_type` tinyint DEFAULT NULL COMMENT '时段类型: 1-上午; 2-下午; 3-晚上;',
                               `class_date` date NOT NULL COMMENT '上课日期',
                               `class_start_time` time NOT NULL COMMENT '上课开始时间',
                               `class_end_time` time NOT NULL COMMENT '上课结束时间',
                               `class_start_date_time` datetime NOT NULL COMMENT '上课开始日期时间',
                               `class_end_date_time` datetime NOT NULL COMMENT '上课结束日期时间',
                               `create_by` varchar(255) DEFAULT NULL COMMENT '创建人',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_by` varchar(255) DEFAULT NULL COMMENT '修改人',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
                               `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除: 0-否; 1-是;',
                               PRIMARY KEY (`id`),
                               KEY `b_timetable_class_date_store_id_index` (`class_date`,`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='门店课表';



