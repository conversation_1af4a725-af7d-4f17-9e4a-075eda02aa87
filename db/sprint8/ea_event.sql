/*
 Navicat Premium Data Transfer

 Source Server         : 开发-RDS-8.0
 Source Server Type    : MySQL
 Source Server Version : 80036
 Source Host           : rm-2zeqteys7mo6uh0cr.mysql.rds.aliyuncs.com:3306
 Source Schema         : ydsf_classconnect_dev

 Target Server Type    : MySQL
 Target Server Version : 80036
 File Encoding         : 65001

 Date: 09/01/2025 13:57:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ea_event
-- ----------------------------
DROP TABLE IF EXISTS `ea_event`;
CREATE TABLE `ea_event`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '事件ID',
  `del_flag` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '是否删除:0-正常;1-删除',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `event_type` tinyint NULL DEFAULT 0 COMMENT '事件类型:0-进入直播间;1-离开直播间;2-发起互动;3-结束互动;4-进入播放器;5-离开播发器;6-播放器翻页;7-学生互动结果',
  `event_time` datetime NULL DEFAULT NULL COMMENT '事件时间',
  `device_type` tinyint NULL DEFAULT 0 COMMENT '设备类型:1-直播端;2-教师端;3-讲师端',
  `device_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备编号',
  `lesson_no` bigint NULL DEFAULT NULL COMMENT '课程编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '课堂事件表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
