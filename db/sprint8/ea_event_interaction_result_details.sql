/*
 Navicat Premium Data Transfer

 Source Server         : 开发-RDS-8.0
 Source Server Type    : MySQL
 Source Server Version : 80036
 Source Host           : rm-2zeqteys7mo6uh0cr.mysql.rds.aliyuncs.com:3306
 Source Schema         : ydsf_classconnect_dev

 Target Server Type    : MySQL
 Target Server Version : 80036
 File Encoding         : 65001

 Date: 09/01/2025 13:57:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ea_event_interaction_result_details
-- ----------------------------
DROP TABLE IF EXISTS `ea_event_interaction_result_details`;
CREATE TABLE `ea_event_interaction_result_details`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `del_flag` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '是否删除:0-正常;1-删除',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建人',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `pid` bigint NULL DEFAULT NULL COMMENT '主事件ID',
  `interaction_type` tinyint NULL DEFAULT -1 COMMENT '互动类型:0-抢红包;1-选择题;2-投票;3-点名;4-勋章',
  `student_id` int NULL DEFAULT NULL COMMENT '学生id',
  `student_name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学生名',
  `student_value` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '学生互动结果',
  `student_status` tinyint NULL DEFAULT NULL COMMENT '学生状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '课堂互动结果明细' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
