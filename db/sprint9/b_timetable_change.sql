create table b_timetable_change
(
    id                  bigint auto_increment comment '主键ID'
        primary key,
    student_id          bigint                                     not null comment '学员ID',
    source_lesson_no    bigint                                     not null comment '原课程编号',
    source_course_id    bigint                                     null comment '原课程ID',
    source_lesson_order int                                        null comment '原课程第几节课',
    source_store_id     bigint                                     not null comment '原门店ID',
    target_lesson_no    bigint                                     not null comment '目标课程编号',
    target_course_id    bigint                                     null comment '目标课程ID',
    target_lesson_order int                                        null comment '目标课程第几节课',
    target_store_id     bigint                                     not null comment '目标门店ID',
    create_by           varchar(255)                               null comment '创建人',
    create_time         datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by           varchar(255)                               null comment '修改人',
    update_time         datetime         default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间',
    del_flag            tinyint unsigned default '0'               not null comment '是否删除: 0-否; 1-是'
)
    comment '调课记录表' row_format = DYNAMIC;

create index idx_create_time
    on ss_course_dev.b_timetable_change (create_time)
    comment '创建时间索引';

create index idx_student_source
    on ss_course_dev.b_timetable_change (student_id, source_lesson_no)
    comment '学员原课程查询索引';

create index idx_student_target
    on ss_course_dev.b_timetable_change (student_id, target_lesson_no)
    comment '学员目标课程查询索引';

