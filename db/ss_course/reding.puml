@startuml

class CourseSchedule {
    == 课表
    +id: int --主键ID
    +scheduleId: int --课程安排ID
    +creationDate: DateTime --创建时间
    +confirmationStatus: String --确认状态
}

class GradeSchedule {
    == 年级课表
    +id: int --主键ID
    +gradeLevel: String --年级
}

class ClassSession {
    == 课程安排
    +id: int --主键ID
    +date: Date --上课日期
    +bookName: String --书籍名称
}

class Lecturer {
    == 主讲老师
    +id: int --主键ID
    +name: String --主讲老师名称
    +gradeLevel: String --年级
}

class Classroom {
    == 教室
    +id: int --主键ID
    +location: String --位置
    +capacity: int --容量
}

class Franchise {
    == 加盟商
    +id: int --主键ID
    +storeName: String --店名
}

CourseSchedule "1" -- "many" GradeSchedule : 包含
GradeSchedule "1" -- "many" ClassSession : 包括
ClassSession "many" -- "1" Lecturer : 教授
ClassSession "many" -- "1" Classroom : 授课
Franchise "1" -- "many" Classroom : 管理

@enduml