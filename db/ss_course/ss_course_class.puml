@startuml
'https://plantuml.com/sequence-diagram
entity "ss_class" {
    班级信息表
    ==
    #id : bigint <<generated>> --主键ID
    *class_name : varchar(255) --班级名称
    *grade : tinyint --年级(字典类型: ss_level)
    *class_state : tinyint <<default:0>> --班级状态: 0-正常; 1-已结业;
    *is_sync_xiaogj : tinyint <<default:1>> --是否同步校管家: 0-否; 1-是;
    class_type : tinyint --班级类型: 0-读书会; 1-点播班;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_auth_room" {
    班级授权教室表
    ==
    #id : bigint <<generated>> --主键ID
    source : bigint --所属门店
    *class_id : bigint --班级ID
    xgj_class_id : varchar(255) --校管家班级ID
    *campus_id : bigint --校区ID
    class_room_id : bigint --教室ID
    device_id : bigint --教室端设备ID
    appointment_time : datetime --预约时间/授权时间
    *xgj_campus_id : varchar(255) --校管家校区ID
    xgj_class_room_id : varchar(255) --校管家教室ID
    class_time_ids : text --课次IDS(代表临时授权课次,多个以英文逗号分隔)
    *appointment_status : tinyint --预约状态:0-未预约;1-已预约
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}


ss_class "1" *-- "many" ss_class_auth_room : 包含

entity "ss_class_auth_room_student" {
    班级授权校区学生表
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    *campus_id : bigint --校区ID
    *student_id : varchar(255) --校管家学生ID
    *student_mobile : varchar(255) --校管家学生手机号
    *student_name : varchar(255) --校管家学生名称
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

ss_class "1" *-- "many" ss_class_auth_room_student : 包含

package "校区" #DDDDDD {

entity "ss_campus" {
    校区表
    ==
    #id : bigint <<generated>> --主键ID
    xgj_campus_id : varchar(255) --校管家校区ID(类型为主讲端时为空)
    region_name : varchar(255) --大区名称
    campus_no : varchar(255) --校区编号
    *campus_name : varchar(255) --校区名称
    *campus_state : tinyint <<default:0>> --校区状态: 0-启用; 1-禁用;
    *campus_type : tinyint --校区类型: 1-主讲端; 2-教室端;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_room" {
    教室信息表
    ==
    #id : bigint <<generated>> --主键ID
    xgj_class_room_id : varchar(255) --校管家教室ID(类型为主讲端时为空)
    *campus_id : bigint --校区ID
    *class_room_name : varchar(255) --教室名称
    *class_room_state : tinyint <<default:0>> --教室状态: 0-启用; 1-禁用;
    *class_room_type : tinyint --教室类型: 1-主讲端; 2-教室端;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

ss_campus "1" *-- "many" ss_class_room : 包含

entity "ss_device" {
    设备表
    ==
    #id : bigint <<generated>> --主键ID
    campus_id : bigint --校区ID
    class_room_id : bigint --教室ID
    device_name : varchar(255) --设备名称
    *device_no : varchar(255) --设备号
    device_type : tinyint --设备类型: 1-主讲端; 2-教室端;
    device_state : tinyint <<default:0>> --设备状态: 0-启用; 1-禁用;
    device_active : int <<default:0>> --设备是否激活:0-未激活;1-已激活:
    device_arrears : int <<default:0>> --设备欠费状态:0-正常;其他状态为欠费
'    (读书会欠费;管理费欠费;设备费欠费;合同欠费;)
    is_on_line : tinyint <<default:0>> --是否在线: 0-否; 1-是;
    indate_forever : int <<default:1>> --设备是否永久:0-否;1-是;
    indate_start : datetime --有效期开始时间
    indate_end : datetime --有效期结束时间
    config_id : bigint <<default:1>> --设备配置表ID
    audio_config_id : bigint --音频配置ID
    agora_recording_type : tinyint --主讲端录课方式:0-页面录制;1-云端录制;
    live_background : varchar(500) --直播背景图路径
    sdk_type : tinyint <<default:1>> --终端SDK版本:0-webSDK;1-Electron
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
    device_uuid : varchar(255) --设备是否是由UUID生成
'    不为空代表注册设备码是由uuid生成
}
}

ss_class_room "1" *-- "1" ss_device : 包含

ss_device "1" *-- "many" ss_class_auth_room : 包含

@end