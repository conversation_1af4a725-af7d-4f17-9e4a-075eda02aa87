@startuml

entity "ss_device" {
    设备表
    ==
    #id : bigint <<generated>> --主键ID
    campus_id : bigint --校区ID
    class_room_id : bigint --教室ID
    device_name : varchar(255) --设备名称
    *device_no : varchar(255) --设备号
    device_type : tinyint --设备类型: 1-主讲端; 2-教室端;
    device_state : tinyint <<default:0>> --设备状态: 0-启用; 1-禁用;
    device_active : int <<default:0>> --设备是否激活:0-未激活;1-已激活:
    device_arrears : int <<default:0>> --设备欠费状态:0-正常; 其他状态为欠费(读书会欠费;管理费欠费;设备费欠费;合同欠费;)
    is_on_line : tinyint <<default:0>> --是否在线: 0-否; 1-是;
    indate_forever : int <<default:1>> --设备是否永久:0-否;1-是;
    indate_start : datetime --有效期开始时间
    indate_end : datetime --有效期结束时间
    config_id : bigint <<default:1>> --设备配置表ID
    audio_config_id : bigint --音频配置ID
    agora_recording_type : tinyint --主讲端录课方式:0-页面录制;1-云端录制;
    live_background : varchar(500) --直播背景图路径
    sdk_type : tinyint <<default:1>> --终端SDK版本:0-webSDK;1-Electron
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
    device_uuid : varchar(255) --设备是否是由UUID生成不为空代表注册设备码是由uuid生成
}

entity "device_info" {
    #id : bigint
    school_name : varchar(255)
    device_name : varchar(255)
    brand : varchar(255)
    cpu : text
    memory : varchar(255)
    os : varchar(255)
    os_type : varchar(255)
    os_version : varchar(255)
    net : varchar(255)
}

entity "franchisee" {
    加盟商表 COLLATE utf8mb4_general_ci
    ==
    #id : int <<generated>> --加盟商id
    name : varchar(64) --加盟商名称
    phone : varchar(255) --联系电话
    agreement_num : varchar(255) --合同编号
    head_name : varchar(255) --负责人
    qrcode : varchar(255) --二维码图片
    *disable : char <<default:'0'>> --禁用状态，0不禁用，1禁用
    *create_by : varchar(255) --创建者
    create_time : datetime
    update_time : datetime
    *deleted : char <<default:'0'>>
    sms_phone : varchar(20)
}

entity "school" {
    #id : int <<generated>> --主键ID
    franchisee_id : int --加盟商id
    name : varchar(255) --加盟校名称
    management_fee : decimal(10, 2) --管理费
    cost_time : datetime --缴费时间
    nickname : varchar(255) --签约智慧约读时的乙方名称（会有公司或者个人姓名）
    no : varchar(32) --校区编号
    standard : decimal(10, 2) --收费标准
    phone : varchar(255) --联系电话
    last_end_date : datetime --上期结算终止时间
    qrcode : varchar(255) --二维码
    *disable : char <<default:'0'>> --禁用状态，0不禁用，1禁用
    *create_by : varchar(255) --创建者
    create_time : datetime --创建时间
    update_time : datetime --更新时间
    *deleted : char <<default:'0'>> --逻辑删除: 0-正常; 1-删除;
    management_fee_discount : decimal(10, 2) <<default:1.00>> --管理费用折扣
}

entity "school_device" {
    #id : bigint <<generated>> --主键ID
    create_time : datetime --创建时间
    update_time : datetime --更新时间
    remak : varchar(255) --备注信息
    del_flag : tinyint <<default:0>> --逻辑删除: 0-正常; 1-删除;
    franchisee_name : varchar(50) --加盟商名称
    franchisee_id : bigint --加盟商ID
    school_name : varchar(50) --校区名称
    school_id : bigint --校区ID
    device_id : bigint --设备ID
    device_name : varchar(50) --设备名称
    device_unique : varchar(50) --设备唯一编号
    device_address : varchar(200) --设备安装地址
    device_expire_date : datetime --设备到期时间
    device_purpose : tinyint --设备类型: 0-正式; 1-试用; 2-弃用; 3-内部;
    device_status : tinyint <<default:0>> --设备状态: 0-正常;1-禁用;
    school_no : varchar(20) --校区编号
    room_id : bigint --教室ID
    room_name : varchar(50) --教室名称
    device_type : tinyint <<default:0>> --设备类型: 0-百家云; 1-双师设备;
}

entity "ss_appointment_class_log" {
    门店预约班级记录表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    source : bigint --所属门店
    *class_id : bigint --班级ID
    *campus_id : bigint --约课校区ID
    *class_room_id : bigint --约课教室ID
    *device_id : bigint --约课设备ID
    appointment_time : datetime --预约时间
    cancel_appointment_time : datetime --取消预约时间
    xgj_campus_id : varchar(255) --校管家校区ID
    xgj_class_room_id : varchar(255) --校管家教室ID
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_auth_room_log" {
    班级/课次授权修改记录表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *business_id : bigint --业务表ID
    *edit_auth_type : tinyint --修改授权类型: 0-班级授权; 1-课次授权;
    old_value : text --旧授权设备ID值(多个教室以英文逗号分割)
    new_value : text --新授权设备ID值(多个教室以英文逗号分割)
    ctime : datetime --授权修改时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_campus" {
    校区表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    xgj_campus_id : varchar(255) --校管家校区ID(类型为主讲端时为空)
    region_name : varchar(255) --大区名称
    campus_no : varchar(255) --校区编号
    *campus_name : varchar(255) --校区名称
    *campus_state : tinyint <<default:0>> --校区状态: 0-启用; 1-禁用;
    *campus_type : tinyint --校区类型: 1-主讲端; 2-教室端;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class" {
    班级信息表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_name : varchar(255) --班级名称
    *grade : tinyint --年级(字典类型: ss_level)
    *class_state : tinyint <<default:0>> --班级状态: 0-正常; 1-已结业;
    *is_sync_xiaogj : tinyint <<default:1>> --是否同步校管家: 0-否; 1-是;
    class_type : tinyint --班级类型: 0-读书会; 1-点播班;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_auth_room" {
    班级授权教室表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    source : bigint --所属门店
    *class_id : bigint --班级ID
    xgj_class_id : varchar(255) --校管家班级ID
    *campus_id : bigint --校区ID
    class_room_id : bigint --教室ID
    device_id : bigint --教室端设备ID
    appointment_time : datetime --预约时间/授权时间
    *xgj_campus_id : varchar(255) --校管家校区ID
    xgj_class_room_id : varchar(255) --校管家教室ID
    class_time_ids : text --课次IDS(代表临时授权课次,多个以英文逗号分隔)
    *appointment_status : tinyint --预约状态:0-未预约;1-已预约
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_auth_room_student" {
    班级授权校区学生表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    *campus_id : bigint --校区ID
    *student_id : varchar(255) --校管家学生ID
    *student_mobile : varchar(255) --校管家学生手机号
    *student_name : varchar(255) --校管家学生名称
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_room" {
    教室信息表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    xgj_class_room_id : varchar(255) --校管家教室ID(类型为主讲端时为空)
    *campus_id : bigint --校区ID
    *class_room_name : varchar(255) --教室名称
    *class_room_state : tinyint <<default:0>> --教室状态: 0-启用; 1-禁用;
    *class_room_type : tinyint --教室类型: 1-主讲端; 2-教室端;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_time" {
    课次信息表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    room_uuid : varchar(255) --声网UUID
    *class_id : bigint --班级ID
    course_schedule_id : bigint --排课ID
    course_schedule_books_id : bigint --排课书籍ID
    course_schedule_rule_id : bigint --排课规则ID
    *attend_class_date : date --上课日期（yyyy-MM-dd）
    *attend_class_start_time : time --上课开始时间（HH:mm）
    *attend_class_end_time : time --上课结束时间（HH:mm）
    *is_sync_agora : tinyint <<default:0>> --是否已同步声网创建课堂: 0-否; 1-是;
    attend_class_type : tinyint --上课类型: 0-直播课; 1-点播课;
    supervision_class_url : varchar(1000) --监课链接url路径
    supervision_class_start_time : datetime --监课开始时间(yyyy-MM-dd HH:mm:ss）
    supervision_class_end_time : datetime --监课结束时间(yyyy-MM-dd HH:mm:ss）
    lecturer_id : bigint --主讲老师ID(ss_lecturer主键ID)
    device_id : bigint --主讲设备ID
    class_room_id : bigint --主讲教室ID
    books_id : varchar(255) --书籍ID
    books_name : varchar(255) --书籍名称
    recording_id : bigint --课程库ID(录播课资源ID)
    lecturer_room_code : varchar(10) --主讲端上课码(上课端标识1 + 5位随机数  例:115329)
    class_room_code : varchar(10) --教室端上课码(教室端标识2 + 5位随机数  例:235329)
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_time_0705" {
    课次信息表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    room_uuid : varchar(255) --声网UUID
    *class_id : bigint --班级ID
    course_schedule_id : bigint --排课ID
    course_schedule_books_id : bigint --排课书籍ID
    course_schedule_rule_id : bigint --排课规则ID
    *attend_class_date : date --上课日期（yyyy-MM-dd）
    *attend_class_start_time : time --上课开始时间（HH:mm）
    *attend_class_end_time : time --上课结束时间（HH:mm）
    *is_sync_agora : tinyint <<default:0>> --是否已同步声网创建课堂: 0-否; 1-是;
    attend_class_type : tinyint --上课类型: 0-直播课; 1-点播课;
    supervision_class_url : varchar(1000) --监课链接url路径
    supervision_class_start_time : datetime --监课开始时间(yyyy-MM-dd HH:mm:ss）
    supervision_class_end_time : datetime --监课结束时间(yyyy-MM-dd HH:mm:ss）
    lecturer_id : bigint --主讲老师ID(ss_lecturer主键ID)
    device_id : bigint --主讲设备ID
    class_room_id : bigint --主讲教室ID
    books_id : varchar(255) --书籍ID
    books_name : varchar(255) --书籍名称
    recording_id : bigint --课程库ID(录播课资源ID)
    lecturer_room_code : varchar(10) --主讲端上课码(上课端标识1 + 5位随机数  例:115329)
    class_room_code : varchar(10) --教室端上课码(教室端标识2 + 5位随机数  例:235329)
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_time_auth_room" {
    课次授权教室表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    *class_time_id : bigint --课次ID
    xgj_class_time_id : varchar(255) --校管家课次ID
    *campus_id : bigint --校区ID
    *class_room_id : bigint --教室ID
    *device_id : bigint --教室端设备ID
    *xgj_campus_id : varchar(255) --校管家校区ID
    *xgj_class_room_id : varchar(255) --校管家教室ID
    xiaogj_delete_log : varchar(255) --校管家排课删除状态
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_time_auth_room_0705" {
    课次授权教室表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    *class_time_id : bigint --课次ID
    xgj_class_time_id : varchar(255) --校管家课次ID
    *campus_id : bigint --校区ID
    *class_room_id : bigint --教室ID
    *device_id : bigint --教室端设备ID
    *xgj_campus_id : varchar(255) --校管家校区ID
    *xgj_class_room_id : varchar(255) --校管家教室ID
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_time_auth_room_history" {
    课次授权教室历史表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    *class_time_id : bigint --课次ID
    xgj_class_time_id : varchar(255) --校管家课次ID
    *campus_id : bigint --校区ID
    *class_room_id : bigint --教室ID
    *device_id : bigint --教室端设备ID
    *xgj_campus_id : varchar(255) --校管家校区ID
    *xgj_class_room_id : varchar(255) --校管家教室ID
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
    *migrated_at : timestamp <<default:CURRENT_TIMESTAMP>> --迁移时间
}

entity "ss_class_time_student" {
    校区上课学生表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    *class_time_id : bigint --课次ID
    *class_time_auth_room_id : bigint --课次授权教室表ID
    *campus_id : bigint --校区ID
    *class_room_id : bigint --教室ID
    *device_id : bigint --教室设备ID
    *student_id : varchar(255) --校管家学生ID
    *student_mobile : varchar(255) --校管家学生手机号
    *student_name : varchar(255) --校管家学生名称
    *interactor_bind_status : tinyint <<default:0>> --互动题绑定状态: 0-未绑定; 1-已绑定;(废弃)
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_class_time_student_history" {
    校区上课学生历史表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    *class_time_id : bigint --课次ID
    *class_time_auth_room_id : bigint --课次授权教室表ID
    *campus_id : bigint --校区ID
    *class_room_id : bigint --教室ID
    *device_id : bigint --教室设备ID
    *student_id : varchar(255) --校管家学生ID
    *student_mobile : varchar(255) --校管家学生手机号
    *student_name : varchar(255) --校管家学生名称
    *interactor_bind_status : tinyint <<default:0>> --互动题绑定状态: 0-未绑定; 1-已绑定;(废弃)
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
    *migrated_at : timestamp <<default:CURRENT_TIMESTAMP>> --迁移时间
}

entity "ss_course_schedule" {
    排课表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    *lecturer_id : bigint --主讲老师ID(ss_lecturer主键ID)
    *class_time_method : tinyint --排课方式: 0-按周排课; 1-按日历排课;
    attend_class_start_date : date --上课开始日期（yyyy-MM-dd）
    attend_class_end_date : date --上课结束日期（yyyy-MM-dd）
    schedule_cap : int --排课上限几次
    *attend_class_type : tinyint --上课类型: 0-直播课; 1-点播课;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_course_schedule_books" {
    排课书籍表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    *course_schedule_id : bigint --排课ID
    how_many_times : int --第几次上课书籍
    books_id : varchar(255) --书籍ID
    books_name : varchar(255) --书籍名称
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_course_schedule_rule" {
    排课规则表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_id : bigint --班级ID
    device_id : bigint --直播设备ID
    course_schedule_id : bigint --排课ID
    attend_class_week : tinyint --上课周几(字典类型:week_type)
    attend_class_start_time : time --上课开始时间（HH:mm）
    attend_class_end_time : time --上课结束时间（HH:mm）
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_current_stream" {
    当前九宫格监听流
    ==
    #id : bigint <<generated>> --主键ID
    room_uuid : varchar(255) --声网UUID
    *class_time_id : bigint --课次ID
    streams : text --当前九屏的流相关
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_device_audio_config" {
    设备音频相关配置
    ==
    #id : bigint <<generated>> --主键ID
    parameters : text --参数配置类:
che.audio.start_debug_recording-本地生成音频文件
che.audio.codec.name-打开OPUS和百家云相似,关闭OPUS是声网独特音质
che.audio.mute.input.channel-忽略右声道杂音
che.audio.current.recording.boostMode-关闭系统音量调整
che.audio.enable.agc-关闭增益调整
che.audio.input.volume-输入音量
    in_class_parameters : varchar(500) --加入频道之后的参数
    adjust_recording_signal_volume : int --音量增益
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
    remark : varchar(500) --备注
}

entity "ss_device_config" {
    设备配置参数表
    ==
    #id : bigint <<generated>> --主键ID
    t_bw : int --教师端大流宽度
    t_bh : int --教师端大流高度
    t_bb : int --教师端大码率
    t_bf : int --教师端大帧率
    t_sw : int --教师端小流宽度
    t_sh : int --教师端小流高度
    t_sb : int --教师端小码率
    t_sf : int --教师端小帧率
    s_bw : int --学生端大流宽度
    s_bh : int --学生端大流高度
    s_bb : int --学生端大码率
    s_bf : int --学生端大帧率
    s_sw : int --学生端小流宽度
    s_sh : int --学生端小流高度
    s_sb : int --学生端小码率
    s_sf : int --学生端小帧率
    t_hd : tinyint --教师是否订阅大流:0-是;1-否;
    s_hd : tinyint --学生端是否订阅大流:0-是;1-否;
    s_show_number : int <<default:8>> --九空格展示学生数量
    remark : varchar(500) --备注
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
    log_enable : tinyint <<default:0>> --日志开关是否开启:0-关闭;1-开启;
}

entity "ss_in_out_log" {
    点播课教室端进出课程记录 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *class_time_id : bigint --课次ID
    *device_id : bigint --教室端设备ID
    *operate_room_type : tinyint --点播课操作类型: 0-进入课程; 1-离开课程;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_interaction_consequence" {
    互动结果表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *interaction_setting_id : bigint --互动设置ID
    *class_time_id : bigint --课次ID
    *device_id : bigint --设备ID
    *campus_id : bigint --校区ID
    *class_room_id : bigint --教室ID
    *student_id : varchar(255) --校管家学生ID
    *student_no : varchar(255) --校管家学号
    *student_name : varchar(255) --校管家学生名称
    integral_number : int --抢红包-抢到积分数量
    answer_option : varchar(10) --答题抢票器-选项
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_interaction_consequence_history" {
    互动结果历史表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *interaction_setting_id : bigint --互动设置ID
    *class_time_id : bigint --课次ID
    *device_id : bigint --设备ID
    *campus_id : bigint --校区ID
    *class_room_id : bigint --教室ID
    *student_id : varchar(255) --校管家学生ID
    *student_no : varchar(255) --校管家学号
    *student_name : varchar(255) --校管家学生名称
    integral_number : int --抢红包-抢到积分数量
    answer_option : varchar(10) --答题抢票器-选项
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
    *migrated_at : timestamp <<default:CURRENT_TIMESTAMP>> --迁移时间
}

entity "ss_interaction_red_packet_detail" {
    红包拆分明细表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *interaction_setting_id : bigint --互动设置ID
    *class_time_id : bigint --课次ID
    device_id : bigint --设备ID
    campus_id : bigint --校区ID
    class_room_id : bigint --教室ID
    *integral_number : int --红包积分数量
    red_packet_number : int --红包数量
    remainder_integral_number : int --平分后剩余积分
    remainder_red_packet_number : int --平分后剩余红包数量
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_interaction_setting" {
    互动设置表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    class_time_id : bigint --课次ID
    *send_type : tinyint --下发类型: 1-主讲端下发; 2-门店端下发;
    *device_id : int --设备ID
    *interaction_type : tinyint --互动类型: 0-计时器; 1-答题抢票器; 2-抢红包;
    timing_time : int --计时器-计时器时间(单位:分钟)
    option_number : int --答题抢票器-选项个数
    answer_countdown : int --答题抢票器-答题倒计时(单位:秒)
    integral_number : int --抢红包-积分数量
    red_packet_num : int --红包个数
    interaction_duration : int --互动持续时间
    key_interval_num : int --互动器按键间隔生效次数
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_lecturer" {
    主讲老师表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *xgj_lecturer_id : varchar(255) --校管家主讲老师ID
    *lecturer_name : varchar(255) --主讲老师名称
    *lecturer_state : tinyint <<default:0>> --主讲老师状态: 0-启用; 1-禁用;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑人
}

entity "ss_operate_event_log" {
    操作事件记录表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *event_type : varchar(255) --操作事件类型
    operate_id : bigint --操作用户ID
    operate_name : varchar(255) --操作用户名称
    *request_param : text --请求参数
    *result_param : text --返回参数
    *ip_address : varchar(255) --ip地址
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_recording" {
    双师录课 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *recording_type : tinyint --录制类型:0-点播课;1-培训会议;
    device_id : bigint --录制设备ID
    grade : tinyint --年级(字典类型: ss_level)
    books_id : varchar(255) --书籍ID
    books_name : varchar(255) --书籍名称
    lecturer_id : bigint --主讲老师ID(ss_lecturer主键ID)
    lecturer_name : varchar(255) --教室名称
    original_course_start_date : datetime --原考勤班级上课开始时间
    original_course_end_date : datetime --原考勤班级上课结束时间
    agora_record_id : varchar(255) --声网录制ID
    room_uuid : varchar(255) --声网房间UUID
    *shelf_status : tinyint <<default:1>> --上下架状态:0-未上架;1-已上架
    *recording_status : tinyint <<default:0>> --录制状态:0-待录制;1-录制中;2-正常录制完成;3-录制作废（重新录制）;4-视频处理中;
    storage_type : tinyint <<default:0>> --资源存储类型:0-OSS;1-VOD
    vod_video_id : varchar(255) --视频点播Vod中videoId
    recording_resources : varchar(255) --录制资源
    agora_cloud_record_id : varchar(255) --声网云端录制id
    cloud_recording_resources : varchar(500) --云端录制资源地址
    agora_cloud_record_individual_resource_id : varchar(500) --声网单流云录制iD
    agora_cloud_record_individual_id : varchar(255) --声网云端录制单流id
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑人
}

entity "ss_screenshot_detail" {
    双师截图明细表
    ==
    #id : bigint <<generated>> --主键ID
    *class_time_id : bigint --课次ID
    *device_id : bigint --教室端设备ID
    resources_name : varchar(255) --资源名称
    resources_path : varchar(255) --资源路径
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑人
    screenshot_time : datetime --截图时间
}

entity "ss_xiaogj_log" {
    校管家日志表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *request_id : varchar(255) --请求ID
    *request_param : text --请求参数
    response_param : text --返回参数
    response_code : int --返回状态码: 200-响应成功; 400-响应失败;
    event_key : varchar(64) --事件类型: class_course-双师排课;
    timestamp : bigint --请求时间戳
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "ss_xiaogj_log_copy1" {
    校管家日志表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *request_id : varchar(255) --请求ID
    *request_param : text --请求参数
    response_param : text --返回参数
    response_code : int --返回状态码: 200-响应成功; 400-响应失败;
    event_key : varchar(64) --事件类型: class_course-双师排课;
    timestamp : bigint --请求时间戳
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "sys_dict_data" {
    字典数据表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *dict_lable : varchar(30) --字典标签
    *dict_value : varchar(1000) --字典健值
    *dict_type : varchar(30) --字典类型
    dict_pvalue : varchar(30) --父级字典健值（对应父级dict_value字段值）
    dict_sort : int --字典排序
    *status : int <<default:0>> --状态: 0-正常；1-停用;
    remark : varchar(300) --备注
    *is_del : int <<default:0>> --是否删除: 0-否；1-是;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
    dtime : datetime --删除时间
}

entity "sys_dict_type" {
    字典类型表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *dict_name : varchar(30) --字典名称
    *dict_type : varchar(30) --字典类型
    *status : int <<default:0>> --状态: 0-正常；1-停用;
    remark : varchar(300) --备注
    *is_del : int <<default:0>> --是否删除: 0-否；1-是;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
    dtime : datetime --删除时间
}

entity "sys_manager" {
    双师系统-管理员表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *uc_manager_id : bigint --用户关联uc中的用户ID
    *mobile : varchar(11) --管理员手机号，用于登录
    *state : tinyint <<default:0>> --用户状态：0-启用; 1-禁用;
    login_ip : varchar(32) --最后登录ip
    last_login_time : datetime --最后登录时间
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "sys_manager_info" {
    双师系统-管理员扩展信息表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    *uc_manager_id : bigint --用户关联uc中的用户ID
    *real_name : varchar(32) --姓名
    *gender : tinyint <<default:0>> --性别: 0-未知; 1-男; 2-女; 
    age : int --年龄
    email : varchar(32) --邮箱地址
    remark : varchar(128) --备注信息
    entry_time : date --入职时间
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "sys_manager_role" {
    双师系统-用户与角色关联表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint UNSIGNED <<generated>> --主键ID
    *uc_manager_id : bigint --用户关联uc中的用户ID
    *role_id : bigint --角色ID
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "sys_menu" {
    双师系统-菜单表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint <<generated>> --主键ID
    title : varchar(255) --菜单名称
    label : varchar(255) --菜单组件
    *parent_id : bigint --父菜单ID, 一级菜单为0
    *type : tinyint --菜单类型: 1-一级目录; 2-功能模块; 
    path_name : varchar(255) --访问路径
    *state : tinyint <<default:0>> --菜单状态: 0-启用; 1-禁用; 
    *is_cache : tinyint <<default:0>> --是否缓存: 0-否; 1-是;
    *is_show : tinyint <<default:0>> --显示状态: 0-展示; 1-隐藏;
    *hide_children : tinyint <<default:0>> --是否展示子菜单: 0-展示; 1-不展示;
    redirect : varchar(255) --页面重定向
    *sort : int --排序
    icon : varchar(50) --目录图标
    remark : varchar(255) --备注
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "sys_role" {
    双师系统-角色表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint UNSIGNED <<generated>> --主键ID
    *name : varchar(32) --名称
    *state : tinyint <<default:0>> --角色状态: 0-启用; 1-禁用; 
    remark : varchar(255) --备注
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "sys_role_menu" {
    双师系统-角色与菜单关联表 COLLATE utf8mb4_general_ci
    ==
    #id : bigint UNSIGNED <<generated>> --主键ID
    *menu_id : bigint --菜单ID
    *role_id : bigint --角色ID
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

entity "xgj_campus" {
    cID : varchar(255)
    cField1 : varchar(255)
}

@enduml
