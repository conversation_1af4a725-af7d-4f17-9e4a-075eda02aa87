@startuml
'https://plantuml.com/sequence-diagram

    entity "ss_class" {
        班级信息表
        ==
        #id : bigint <<generated>> --主键ID
        *class_name : varchar(255) --班级名称
        *grade : tinyint --年级(字典类型: ss_level)
        *class_state : tinyint <<default:0>> --班级状态: 0-正常; 1-已结业;
        *is_sync_xiaogj : tinyint <<default:1>> --是否同步校管家: 0-否; 1-是;
        class_type : tinyint --班级类型: 0-读书会; 1-点播班;
        ctime : datetime --创建时间
        creator : varchar(64) --创建者
        mtime : datetime --编辑时间
        modifer : varchar(64) --编辑者
    }

    entity "ss_course_schedule" {
        排课表
        ==
        #id : bigint <<generated>> --主键ID
        *class_id : bigint --班级ID
        *lecturer_id : bigint --主讲老师ID(ss_lecturer主键ID)
        *class_time_method : tinyint --排课方式: 0-按周排课; 1-按日历排课;
        attend_class_start_date : date --上课开始日期（yyyy-MM-dd）
        attend_class_end_date : date --上课结束日期（yyyy-MM-dd）
        schedule_cap : int --排课上限几次
        *attend_class_type : tinyint --上课类型: 0-直播课; 1-点播课;
        ctime : datetime --创建时间
        creator : varchar(64) --创建者
        mtime : datetime --编辑时间
        modifer : varchar(64) --编辑者
    }
ss_class "1" *-- "many" ss_course_schedule : 包含
    entity "ss_course_schedule_rule" {
            排课规则表
            ==
            #id : bigint <<generated>> --主键ID
            *class_id : bigint --班级ID
            device_id : bigint --直播设备ID
            course_schedule_id : bigint --排课ID
            attend_class_week : tinyint --上课周几(字典类型:week_type)
            attend_class_start_time : time --上课开始时间（HH:mm）
            attend_class_end_time : time --上课结束时间（HH:mm）
            ctime : datetime --创建时间
            creator : varchar(64) --创建者
            mtime : datetime --编辑时间
            modifer : varchar(64) --编辑者
        }

        ss_course_schedule "1" *-- "many" ss_course_schedule_rule : 包含

    entity "ss_class_time" {
        课次信息表
        ==
        #id : bigint <<generated>> --主键ID
        room_uuid : varchar(255) --声网UUID
        *class_id : bigint --班级ID
        course_schedule_id : bigint --排课ID
        course_schedule_books_id : bigint --排课书籍ID
        course_schedule_rule_id : bigint --排课规则ID
        *attend_class_date : date --上课日期（yyyy-MM-dd）
        *attend_class_start_time : time --上课开始时间（HH:mm）
        *attend_class_end_time : time --上课结束时间（HH:mm）
        *is_sync_agora : tinyint <<default:0>> --是否已同步声网创建课堂:
'         0-否; 1-是;
        attend_class_type : tinyint --上课类型: 0-直播课; 1-点播课;
        supervision_class_url : varchar(1000) --监课链接url路径
        supervision_class_start_time : datetime --监课开始时间
'        (yyyy-MM-dd HH:mm:ss）
        supervision_class_end_time : datetime --监课结束时间
'        (yyyy-MM-dd HH:mm:ss）
        lecturer_id : bigint --主讲老师ID(ss_lecturer主键ID)
        device_id : bigint --主讲设备ID
        class_room_id : bigint --主讲教室ID
        books_id : varchar(255) --书籍ID
        books_name : varchar(255) --书籍名称
        recording_id : bigint --课程库ID(录播课资源ID)
        lecturer_room_code : varchar(10) --主讲端上课码
'        (上课端标识1 + 5位随机数  例:115329)
        class_room_code : varchar(10) --教室端上课码
'        (教室端标识2 + 5位随机数  例:235329)
        ctime : datetime --创建时间
        creator : varchar(64) --创建者
        mtime : datetime --编辑时间
        modifer : varchar(64) --编辑者
    }

    ss_course_schedule "1" *--right "many" ss_class_time : 包含

    entity "ss_class_time_auth_room" {
        课次授权教室表
        ==
        #id : bigint <<generated>> --主键ID
        *class_id : bigint --班级ID
        *class_time_id : bigint --课次ID
        xgj_class_time_id : varchar(255) --校管家课次ID
        *campus_id : bigint --校区ID
        *class_room_id : bigint --教室ID
        *device_id : bigint --教室端设备ID
        *xgj_campus_id : varchar(255) --校管家校区ID
        *xgj_class_room_id : varchar(255) --校管家教室ID
        xiaogj_delete_log : varchar(255) --校管家排课删除状态
        ctime : datetime --创建时间
        creator : varchar(64) --创建者
        mtime : datetime --编辑时间
        modifer : varchar(64) --编辑者
    }


    ss_class_time "1" *-- "many" ss_class_time_auth_room : 包含

    entity "ss_class_time_student" {
        校区上课学生表
        ==
        #id : bigint <<generated>> --主键ID
        *class_id : bigint --班级ID
        *class_time_id : bigint --课次ID
        *class_time_auth_room_id : bigint --课次授权教室表ID
        *campus_id : bigint --校区ID
        *class_room_id : bigint --教室ID
        *device_id : bigint --教室设备ID
        *student_id : varchar(255) --校管家学生ID
        *student_mobile : varchar(255) --校管家学生手机号
        *student_name : varchar(255) --校管家学生名称
        *interactor_bind_status : tinyint --互动题绑定状态
'        : 0-未绑定; 1-已绑定;(废弃)
        ctime : datetime --创建时间
        creator : varchar(64) --创建者
        mtime : datetime --编辑时间
        modifer : varchar(64) --编辑者
    }

ss_class_time_auth_room "1" *-- "many" ss_class_time_student : 包含



'课次授权教室
entity "ss_class_auth_room" {
    班级授权教室表
    ==
    #id : bigint <<generated>> --主键ID
    source : bigint --所属门店
    *class_id : bigint --班级ID
    xgj_class_id : varchar(255) --校管家班级ID
    *campus_id : bigint --校区ID
    class_room_id : bigint --教室ID
    device_id : bigint --教室端设备ID
    appointment_time : datetime --预约时间/授权时间
    *xgj_campus_id : varchar(255) --校管家校区ID
    xgj_class_room_id : varchar(255) --校管家教室ID
    class_time_ids : text --课次IDS(代表临时授权课次,多个以英文逗号分隔)
    *appointment_status : tinyint --预约状态:0-未预约;1-已预约
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑者
}

 ss_class_time "1" *--right "many" ss_class_auth_room : 包含

@enduml