@startuml
'https://plantuml.com/sequence-diagram

entity "ss_recording" {
    双师录课
    ==
    #id : bigint <<generated>> --主键ID
    *recording_type : tinyint --录制类型:0-点播课;1-培训会议;
    device_id : bigint --录制设备ID
    grade : tinyint --年级(字典类型: ss_level)
    books_id : varchar(255) --书籍ID
    books_name : varchar(255) --书籍名称
    lecturer_id : bigint --主讲老师ID(ss_lecturer主键ID)
    lecturer_name : varchar(255) --主讲老师名称
    original_course_start_date : datetime --原考勤班级上课开始时间
    original_course_end_date : datetime --原考勤班级上课结束时间
    agora_record_id : varchar(255) --声网录制ID
    room_uuid : varchar(255) --声网房间UUID
    *shelf_status : tinyint <<default:1>> --上下架状态:0-未上架;1-已上架
    *recording_status : tinyint <<default:0>> --录制状态:
    0-待录制;1-录制中;2-正常录制完成;3-录制作废（重新录制）;4-视频处理中;
    storage_type : tinyint <<default:0>> --资源存储类型:0-OSS;1-VOD
    vod_video_id : varchar(255) --视频点播Vod中videoId
    recording_resources : varchar(255) --录制资源
    agora_cloud_record_id : varchar(255) --声网云端录制id
    cloud_recording_resources : varchar(500) --云端录制资源地址
    agora_cloud_record_individual_resource_id : varchar(500) --声网单流云录制iD
    agora_cloud_record_individual_id : varchar(255) --声网云端录制单流id
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑人
}

entity "ss_lecturer" {
    主讲老师表
    ==
    #id : bigint <<generated>> --主键ID
    *xgj_lecturer_id : varchar(255) --校管家主讲老师ID
    *lecturer_name : varchar(255) --主讲老师名称
    *lecturer_state : tinyint <<default:0>> --主讲老师状态: 0-启用; 1-禁用;
    ctime : datetime --创建时间
    creator : varchar(64) --创建者
    mtime : datetime --编辑时间
    modifer : varchar(64) --编辑人
}

ss_lecturer "1" *-- "many" ss_recording : 包含

@endum