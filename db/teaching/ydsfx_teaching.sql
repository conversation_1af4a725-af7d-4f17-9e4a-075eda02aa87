create database `ydsfx_teaching` default character set utf8mb4 collate utf8mb4_0900_ai_ci;

-- 2024-10-31 sprint 2 新增表
CREATE TABLE `book_name`
(
    `id`            int unsigned NOT NULL AUTO_INCREMENT,
    `title`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '书名',
    `author`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '作者',
    `version_count` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '书籍版本数',
    `del_flag`      tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '创建人',
    `create_time`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '修改人',
    `update_time`   datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_name` (`title`,`author`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='书名表';


CREATE TABLE `book_stage`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT,
    `stage_id`    int unsigned NOT NULL COMMENT '阶段/书单ID',
    `book_id`     int unsigned NOT NULL COMMENT '书籍ID',
    `del_flag`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
    `create_time` datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '修改人',
    `update_time` datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='书籍适配阶段表';


CREATE TABLE `book_version`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT,
    `book_id`     int unsigned NOT NULL COMMENT '书籍ID',
    `isbn`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'ISBN号',
    `press`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT '' COMMENT '出版社',
    `translator`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT '' COMMENT '译者',
    `source`      tinyint unsigned NOT NULL DEFAULT '1' COMMENT '来源',
    `cover`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT '' COMMENT '封面图片',
    `del_flag`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除（0为不删除，1为删除）',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT '' COMMENT '创建人',
    `create_time` datetime                                                              DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci         DEFAULT '' COMMENT '修改人',
    `update_time` datetime                                                              DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uqx_isbn` (`isbn`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='书籍版本表';


CREATE TABLE `book_version_stage`
(
    `id`              int unsigned NOT NULL AUTO_INCREMENT,
    `stage_id`        int unsigned NOT NULL COMMENT '阶段ID',
    `book_id`         int unsigned NOT NULL COMMENT '书籍ID',
    `book_version_id` int unsigned NOT NULL COMMENT '书籍版本id',
    `del_flag`        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
    `create_time`     datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '修改人',
    `update_time`     datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='书籍版本适配阶段表';


CREATE TABLE `course`
(
    `id`             int unsigned NOT NULL AUTO_INCREMENT,
    `course_code`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '课程编码',
    `course_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课程产品名称',
    `stage_id`       int unsigned NOT NULL COMMENT '阶段ID',
    `lesson_count`   int unsigned NOT NULL COMMENT '课节数量',
    `publish_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '发布状态,0:未发布  1：已发布',
    `del_flag`       tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '创建人',
    `create_time`    datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '修改人',
    `update_time`    datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`course_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='课程表';


CREATE TABLE `course_pub`
(
    `id`             int                                                           NOT NULL AUTO_INCREMENT,
    `course_id`      int                                                           NOT NULL COMMENT '课程Id',
    `version`        int unsigned NOT NULL DEFAULT '1' COMMENT '版本',
    `course_code`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL COMMENT '课程编码',
    `course_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课程产品名称',
    `stage_id`       int unsigned NOT NULL COMMENT '阶段ID',
    `lesson_count`   int unsigned NOT NULL COMMENT '课节数量',
    `publish_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '发布状态',
    `del_flag`       tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '创建人',
    `create_time`    datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '修改人',
    `update_time`    datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='发版课程表';


CREATE TABLE `course_version`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT,
    `course_id`   int      NOT NULL COMMENT '课程编码',
    `attr`        json                                                          DEFAULT NULL COMMENT '附属信息',
    `status`      tinyint  NOT NULL COMMENT '状态,0:未发布 1：已发布',
    `del_flag`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
    `create_time` datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '修改人',
    `update_time` datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='课程发布记录表';


CREATE TABLE `lesson`
(
    `id`             int                                                           NOT NULL AUTO_INCREMENT,
    `course_id`      int                                                           NOT NULL COMMENT '课程Id',
    `lesson_order`   int unsigned NOT NULL DEFAULT '1' COMMENT '课程排序',
    `lesson_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课节名称',
    `book_id`        int unsigned DEFAULT NULL COMMENT '关联书籍',
    `courseware_id`  int unsigned DEFAULT NULL COMMENT '关联课件',
    `type`           tinyint unsigned NOT NULL DEFAULT '0' COMMENT '课节类型，0：正式课， 1：试听课',
    `publish_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '发布状态，0:未发布 1：已发布 ',
    `can_publish`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否可发布，0：不可发布，1:可发布',
    `del_flag`       tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '创建人',
    `create_time`    datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL COMMENT '修改人',
    `update_time`    datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='课程章节表';


CREATE TABLE `lesson_pub`
(
    `id`             int unsigned NOT NULL AUTO_INCREMENT,
    `lesson_id`      int unsigned NOT NULL DEFAULT 0 COMMENT '课程章节ID',
    `course_id`      int                                                           NOT NULL COMMENT '课程Id',
    `version`        int unsigned DEFAULT '1' COMMENT '版本',
    `lesson_order`   int unsigned NOT NULL COMMENT '课程排序',
    `lesson_name`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '课节名称',
    `book_id`        int unsigned NOT NULL COMMENT '关联书籍',
    `courseware_id`  int unsigned NOT NULL COMMENT '关联课件',
    `type`           tinyint unsigned NOT NULL DEFAULT '0' COMMENT '课节类型，0：正式课， 1：试听课',
    `publish_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '发布状态，0:未发布 1：已发布 ',
    `can_publish`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否可发布，0：不可发布，1:可发布',
    `del_flag`       tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '创建人',
    `create_time`    datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '修改人',
    `update_time`    datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='课程章节发布表';


CREATE TABLE `stage`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT,
    `stage_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '名称',
    `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '描述',
    `stage_order` int                                                           NOT NULL DEFAULT '0' COMMENT '排序字段',
    `product_id`  tinyint unsigned NOT NULL DEFAULT '1' COMMENT '产品ID 1双师',
    `del_flag`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '创建人',
    `create_time` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT '' COMMENT '修改人',
    `update_time` datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='阶段/书单';


BEGIN;
INSERT INTO `stage` (`id`, `stage_name`, `description`, `stage_order`, `product_id`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (1, 'LV1', '适用于LV1阶段课程的书单集合', 1, 1, 0, 'admin', '2024-10-25 09:49:20', '', '2024-10-25 11:31:05');
INSERT INTO `stage` (`id`, `stage_name`, `description`, `stage_order`, `product_id`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (2, 'LV2', '适用于LV2阶段课程的书单集合', 2, 1, 0, 'admin', '2024-10-25 09:49:58', '', '2024-10-25 11:31:05');
INSERT INTO `stage` (`id`, `stage_name`, `description`, `stage_order`, `product_id`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (3, 'LV3', '适用于LV3阶段课程的书单集合', 3, 1, 0, 'admin', '2024-10-25 09:50:00', '', '2024-10-25 11:31:05');
INSERT INTO `stage` (`id`, `stage_name`, `description`, `stage_order`, `product_id`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (4, 'LV4', '适用于LV4阶段课程的书单集合', 4, 1, 0, 'admin', '2024-10-25 09:50:00', '', '2024-10-25 11:31:05');
INSERT INTO `stage` (`id`, `stage_name`, `description`, `stage_order`, `product_id`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (5, 'LV5', '适用于LV5阶段课程的书单集合', 5, 1, 0, 'admin', '2024-10-25 09:50:02', '', '2024-10-25 11:31:05');
INSERT INTO `stage` (`id`, `stage_name`, `description`, `stage_order`, `product_id`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (6, 'LV6', '适用于LV6阶段课程的书单集合', 6, 1, 0, 'admin', '2024-10-25 09:50:04', '', '2024-10-25 11:31:05');
INSERT INTO `stage` (`id`, `stage_name`, `description`, `stage_order`, `product_id`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (7, 'LV7', '适用于LV7阶段课程的书单集合', 7, 1, 0, 'admin', '2024-10-25 09:50:05', '', '2024-10-25 11:31:05');
COMMIT;

CREATE TABLE `trace_log`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT,
    `type`        tinyint unsigned NOT NULL COMMENT '类型 1:书名 2:版本 3:课程 4:课节',
    `operate`     tinyint unsigned NOT NULL DEFAULT '0' COMMENT '操作类型 1 添加 2修改 3删除 等',
    `old_data`    json                                                          DEFAULT NULL COMMENT '操作前',
    `new_data`    json     NOT NULL COMMENT '操作后',
    `del_flag`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
    `create_time` datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '修改人',
    `update_time` datetime NOT NULL                                             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='操作留痕表';

-- 2024/11/19 sprint3新增表
ALTER TABLE `book_name`
    ADD COLUMN `courseware_count` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '课件数量' AFTER `version_count`;
ALTER TABLE `lesson`
    ADD COLUMN `courseware_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '课件名称' AFTER `courseware_id`;
ALTER TABLE `lesson`
    ADD COLUMN `press` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出版社名称' AFTER `courseware_name`;

CREATE TABLE `courseware` (
                              `id` int NOT NULL AUTO_INCREMENT,
                              `courseware_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '课件名称',
                              `book_id` int unsigned NOT NULL COMMENT '关联书籍',
                              `book_version_id` int unsigned NOT NULL COMMENT '关联书籍版本',
                              `publish_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '发布状态，0:未发布 1：已发布 ',
                              `version` int unsigned DEFAULT NULL COMMENT '回显版本',
                              `publish_time` datetime DEFAULT NULL COMMENT '版本发布时间',
                              `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                              `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='课件表';

CREATE TABLE `courseware_data` (
                                   `id` int NOT NULL AUTO_INCREMENT,
                                   `courseware_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '课件名称',
                                   `courseware_id` int unsigned NOT NULL COMMENT '关联课件ID',
                                   `data_template_id` int unsigned NOT NULL COMMENT '关联课件模版ID',
                                   `details` json NOT NULL COMMENT '详细数据',
                                   `can_publish` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '发布状态，0:不可发布 1：可发布 ',
                                   `type` tinyint unsigned DEFAULT NULL COMMENT '类型 课件类型 与课件模版一致',
                                   `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                   `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='资料表';

CREATE TABLE `courseware_data_pub` (
                                       `id` int NOT NULL AUTO_INCREMENT,
                                       `version` int unsigned NOT NULL DEFAULT '1' COMMENT '版本',
                                       `courseware_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '课件名称',
                                       `courseware_id` int unsigned NOT NULL COMMENT '关联课件ID',
                                       `data_template_id` int unsigned NOT NULL COMMENT '关联课件模版ID',
                                       `type` tinyint unsigned NOT NULL COMMENT '类型 课件类型 与课件模版一致',
                                       `details` json NOT NULL COMMENT '详细数据',
                                       `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                       `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='发布资料表';

CREATE TABLE `courseware_data_step_details` (
                                                `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                `courseware_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '关联课件名称',
                                                `courseware_id` int unsigned NOT NULL COMMENT '关联课件ID',
                                                `courseware_data_id` int unsigned NOT NULL COMMENT '关联课件内容ID',
                                                `step_id` int unsigned NOT NULL COMMENT '教学页所属环节ID',
                                                `details` json NOT NULL COMMENT '详细数据',
                                                `type` int unsigned NOT NULL DEFAULT '0' COMMENT '类型,0:视频  1:图片  999:其他\r\n',
                                                `page_template_id` int unsigned NOT NULL DEFAULT '0' COMMENT '教学页模版ID',
                                                `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                                `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `uq_ids` (`courseware_id`,`courseware_data_id`,`step_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='资料数据和教学环节详情表';

CREATE TABLE `courseware_data_step_details_pub` (
                                                    `id` int NOT NULL AUTO_INCREMENT,
                                                    `courseware_data_step_details_id` int NOT NULL COMMENT '原details表id',
                                                    `courseware_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '课件名称',
                                                    `courseware_id` int unsigned NOT NULL COMMENT '关联课件ID',
                                                    `courseware_data_id` int unsigned NOT NULL COMMENT '关联课件内容ID',
                                                    `type` int DEFAULT NULL COMMENT '类型,0:视频  1:图片  999:其他',
                                                    `step_id` int unsigned NOT NULL COMMENT '环节ID',
                                                    `details` json NOT NULL COMMENT '详细数据',
                                                    `page_template_id` int unsigned NOT NULL DEFAULT '0' COMMENT '教学页模版ID',
                                                    `version` int NOT NULL COMMENT '版本',
                                                    `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                                    `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                    `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                                    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='资料数据和教学环节详情版本表';

CREATE TABLE `courseware_step` (
                                   `id` int NOT NULL AUTO_INCREMENT,
                                   `courseware_data_id` int DEFAULT NULL COMMENT '课件内容Id',
                                   `courseware_id` int unsigned NOT NULL COMMENT '关联课件ID',
                                   `step_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '课件名称',
                                   `step_parent` int NOT NULL COMMENT '父ID',
                                   `step_order` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
                                   `type` tinyint NOT NULL COMMENT '环节类型，1是环节，2是页面',
                                   `page_template_id` int unsigned NOT NULL DEFAULT '0' COMMENT '教学页模版ID',
                                   `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                   `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='教学环节/页面表';

CREATE TABLE `courseware_step_pub` (
                                       `id` int NOT NULL AUTO_INCREMENT,
                                       `step_id` int DEFAULT NULL COMMENT '原step表id',
                                       `courseware_id` int unsigned NOT NULL COMMENT '关联课件ID',
                                       `courseware_data_id` int DEFAULT NULL COMMENT '课件内容Id',
                                       `step_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '课件名称',
                                       `step_parent` int NOT NULL COMMENT '父ID',
                                       `step_order` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
                                       `version` int unsigned NOT NULL COMMENT '版本',
                                       `type` tinyint NOT NULL COMMENT '环节类型，1是环节，2是页面',
                                       `page_template_id` int unsigned NOT NULL DEFAULT '0' COMMENT '教学页模版ID',
                                       `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                       `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                       `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='教学环节版本表';

CREATE TABLE `courseware_step_template` (
                                            `id` int NOT NULL AUTO_INCREMENT,
                                            `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模版名称',
                                            `details` json NOT NULL COMMENT '详细数据',
                                            `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                            `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='教学环节模版表';

CREATE TABLE `courseware_version` (
                                      `id` int NOT NULL AUTO_INCREMENT,
                                      `book_id` int unsigned NOT NULL COMMENT '关联书籍',
                                      `book_version_id` int unsigned NOT NULL COMMENT '关联版本',
                                      `courseware_id` int unsigned NOT NULL COMMENT '关联课件',
                                      `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                      `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='课件发布表';

CREATE TABLE `data_template` (
                                 `id` int NOT NULL AUTO_INCREMENT,
                                 `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '资料模版名称',
                                 `sort_order` int unsigned NOT NULL COMMENT '排序',
                                 `required` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否必须 1是 0否',
                                 `type` tinyint unsigned NOT NULL COMMENT '类型 1:编辑 2:直接上传',
                                 `file_metadata` json DEFAULT NULL COMMENT '上传文件限制',
                                 `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                 `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                 `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                 `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='课件共用资料表';


CREATE TABLE `teaching_page_template` (
                                          `id` int NOT NULL AUTO_INCREMENT,
                                          `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '教学页模版名称',
                                          `type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '分类 ',
                                          `category` tinyint NOT NULL DEFAULT '0' COMMENT '类型',
                                          `view_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '页面url',
                                          `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '缩略图',
                                          `attr` json NOT NULL COMMENT '配置信息',
                                          `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
                                          `enabled` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用，1 启用 0 禁用',
                                          `page_order` tinyint unsigned DEFAULT '0' COMMENT '排序',
                                          `del_flag` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除',
                                          `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '创建人',
                                          `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='教学页模版表';



ALTER TABLE `courseware_data_pub` ADD COLUMN `courseware_data_id` int NOT NULL COMMENT '资料表ID' AFTER `id`;
ALTER TABLE `lesson_pub` ADD COLUMN `lesson_id` int NOT NULL COMMENT '课程章节ID' AFTER `id`;
ALTER TABLE `teaching_page_template`
    ADD COLUMN `enabled` tinyint NOT NULL DEFAULT 1 COMMENT '是否启用，1 启用 0 禁用' AFTER `remark`;



ALTER TABLE `courseware_data_step_details` CHANGE COLUMN `type` `teaching_page_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '教学页类型' AFTER `details`,
    ADD COLUMN `teaching_page_category` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '教学页分类' AFTER `details`;

ALTER TABLE `courseware_data_step_details_pub` CHANGE COLUMN `type` `teaching_page_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '教学页类型' AFTER `details`,
    ADD COLUMN `teaching_page_category` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '教学页分类' AFTER `details`;


-- 2024/11/27 sprint4 新增字段或表
ALTER TABLE `course`
    ADD COLUMN `version` int UNSIGNED NULL DEFAULT NULL COMMENT '课程最新版本' AFTER `publish_status`,
ADD COLUMN `publish_time` datetime(0) NULL DEFAULT NULL COMMENT '课程发布时间' AFTER `version`;


ALTER TABLE `courseware_version`
    ADD COLUMN `courseware_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '课件名称' AFTER `id`;

ALTER TABLE `teaching_page_template`
    ADD COLUMN `category` tinyint NOT NULL DEFAULT 0 COMMENT '类型' AFTER `type`;

ALTER TABLE `teaching_page_template`
    ADD COLUMN `view_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '页面url' AFTER `category`;

ALTER TABLE `courseware_data_step_details`
    ADD COLUMN `teaching_page_category` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '教学页分类' AFTER `details`,
ADD COLUMN `teaching_page_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '教学页类型' AFTER `teaching_page_category`;
ALTER TABLE `courseware_data_step_details`
DROP COLUMN `type`;

ALTER TABLE `courseware_data_step_details_pub`
    ADD COLUMN `teaching_page_category` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '教学页分类' AFTER `details`,
ADD COLUMN `teaching_page_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '教学页类型' AFTER `teaching_page_category`;
ALTER TABLE `courseware_data_step_details_pub`
DROP COLUMN `type`;

-- 2024/12/17 sprint5 新增字段或表
ALTER TABLE `course`
    ADD COLUMN `disable` tinyint NOT NULL DEFAULT 0 COMMENT '是否停用(0未停用，1停用)' AFTER `publish_status`;

ALTER TABLE `course_version`
    ADD COLUMN `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL AFTER `update_time`;

-- 2025/01/10 sprint8新增字段或表
ALTER TABLE `courseware_data_step_details_pub`
    MODIFY COLUMN `details` json NOT NULL COMMENT '详细数据' AFTER `step_id`;