server:
  port: 8848
  tomcat:
    basedir: logs
  error:
    include-message: always
db:
  num: 1
  user: ${MYSQL_USER:root}
  password: ${MYSQL_PWD:123456}
  url:
    0: jdbc:mysql://${MYSQL_HOST:ydsf-mysql}:${MYSQL_PORT:3306}/${MYSQL_DB:ydsfx_config}?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true&allowPublicKeyRetrieval=true
  pool:
    config:
      connectionTimeout: 30000
      validationTimeout: 10000

nacos:
  core:
    auth:
      server:
        identity:
          key: serverIdentity
          value: security
      system.type: nacos
      plugin.nacos.token.secret.key: SecretKey012345678901234567890123456789012345678901234567890123456789
  security:
    ignore:
      urls: /actuator/**,/,/error,/**/*.css,/**/*.js,/**/*.html,/**/*.map,/**/*.svg,/**/*.png,/**/*.ico,/console-fe/public/**,/v1/auth/**,/v1/console/health/**,/actuator/**,/v1/console/server/**

spring:
  profiles:
    active: @profiles.active@
  datasource:
    platform: mysql  #这个过期属性不能修改，nacos 代码对此有硬编码
  security:
    enabled: true
  boot: # 接入 spring boot admin
    admin:
      client:
        url: http://ydsf-monitor:5001
        username: pig
        password: pig
        instance:
          service-host-type: ip
  application:
    name: @project.artifactId@

useAddressServer: true

management:
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    export:
      influx:
        enabled: false
      elastic:
        enabled: false
