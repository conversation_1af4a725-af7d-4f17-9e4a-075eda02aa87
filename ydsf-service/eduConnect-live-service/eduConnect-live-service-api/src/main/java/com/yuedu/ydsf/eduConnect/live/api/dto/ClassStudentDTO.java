package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.eduConnect.live.api.valid.ClassStudentValidGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.Data;

/**
 * 班级学生
 * <AUTHOR>
 * @date 2025/1/6 15:08
 */
@Data
@Schema(description = "班级学生传输类")
public class ClassStudentDTO implements Serializable {

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Integer classId;

    /**
     * 学生名称/学生手机号 多条件搜索
     */
    @Schema(description = "学生名称/学生手机号 多条件搜索")
    private String searchContent;

    /**
     * 门店ID
     */
    @Schema(description="门店ID")
    @NotNull(groups = {ClassStudentValidGroup.GetStudentListByClassIdGroup.class},message = "门店ID不能为空")
    private Long storeId;

    /**
     * 课表编号
     */
    @Schema(description="课表编号")
    @NotNull(groups = {ClassStudentValidGroup.GetStudentListByClassIdGroup.class},message = "课表编号不能为空")
    private Long lessonNo;

}
