package com.yuedu.ydsf.eduConnect.live.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 门店已约直播课 传输类
 *
 * <AUTHOR>
 * @date 2025-01-08 09:28:55
 */
@Data
@Schema(description = "门店已约直播课传输类")
public class CourseLiveDTO implements Serializable {


	/**
	* 主键ID
	*/
    @Schema(description="主键ID")
    private Long id;

	/**
	* 门店ID
	*/
    @Schema(description="门店ID")
    private Long storeId;

	/**
	* 教学计划ID
	*/
    @Schema(description="教学计划ID")
    private Long teachingPlanId;

	/**
	* 上课时段ID
	*/
    @Schema(description="上课时段ID")
    private Long timeSlotId;

	/**
	* 阶段
	*/
    @Schema(description="阶段")
    private Integer stage;

	/**
	* 班级ID
	*/
    @Schema(description="班级ID")
    private Long classId;

	/**
	* 教室ID
	*/
    @Schema(description="教室ID")
    private Long classroomId;

	/**
	* 指导老师ID
	*/
    @Schema(description="指导老师ID")
    private Long teacherId;

	/**
	* 创建人
	*/
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 是否删除: 0-否; 1-是;
	*/
    @Schema(description="是否删除: 0-否; 1-是;")
    private Integer delFlag;
}
