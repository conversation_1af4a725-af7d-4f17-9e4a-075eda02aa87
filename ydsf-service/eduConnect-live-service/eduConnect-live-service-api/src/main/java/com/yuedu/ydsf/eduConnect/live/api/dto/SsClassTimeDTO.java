package com.yuedu.ydsf.eduConnect.live.api.dto;

import com.yuedu.ydsf.common.core.util.V_A_E;
import com.yuedu.ydsf.common.core.util.V_E;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import jakarta.validation.constraints.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

/**
* 课次信息表
*
* <AUTHOR>
* @date  2024/11/01
*/
@Data
@Schema(description = "课次信息表传输对象")
public class SsClassTimeDTO implements Serializable {


    /**
     * 主键ID
     */
    @NotNull(groups = {V_E.class}, message = "主键ID不能为空")
    private Long id;



    /**
     * 声网UUID
     */
    @Schema(description = "声网UUID")
    @Length(groups = {V_A_E.class }, max =255 ,message = "声网UUID长度不能大于255")
    private String roomUuid;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    @NotNull(groups = {V_A_E.class }, message = "班级ID不能为空")
    private Long classId;

    /**
     * 排课ID
     */
    @Schema(description = "排课ID")
    private Long courseScheduleId;

    /**
     * 排课书籍ID
     */
    @Schema(description = "排课书籍ID")
    private Long courseScheduleBooksId;

    /**
     * 排课规则ID
     */
    @Schema(description = "排课规则ID")
    private Long courseScheduleRuleId;

    /**
     * 上课日期（yyyy-MM-dd）
     */
    @Schema(description = "上课日期（yyyy-MM-dd）")
    @NotNull(groups = {V_A_E.class }, message = "上课日期（yyyy-MM-dd）不能为空")
    private LocalDateTime attendClassDate;

    /**
     * 上课开始时间（HH:mm）
     */
    @Schema(description = "上课开始时间（HH:mm） 字典类型：attend_class_start_time" ,type = "attend_class_start_time")
    @NotNull(groups = {V_A_E.class }, message = "上课开始时间（HH不能为空")
    private LocalDateTime attendClassStartTime;

    /**
     * 上课结束时间（HH:mm）
     */
    @Schema(description = "上课结束时间（HH:mm） 字典类型：attend_class_end_time" ,type = "attend_class_end_time")
    @NotNull(groups = {V_A_E.class }, message = "上课结束时间（HH不能为空")
    private LocalDateTime attendClassEndTime;

    /**
     * 是否已同步声网创建课堂: 0-否; 1-是;
     */
    @Schema(description = "是否已同步声网创建课堂: 0-否; 1-是; 字典类型：is_sync_agora" ,type = "is_sync_agora", defaultValue = "0")
    @NotNull(groups = {V_A_E.class }, message = "是否已同步声网创建课堂不能为空")
    private Integer isSyncAgora;

    /**
     * 上课类型: 0-直播课; 1-点播课;
     */
    @Schema(description = "上课类型: 0-直播课; 1-点播课; 字典类型：attend_class_type" ,type = "attend_class_type")
    private Integer attendClassType;

    /**
     * 监课链接url路径
     */
    @Schema(description = "监课链接url路径")
    @Length(groups = {V_A_E.class }, max =1000 ,message = "监课链接url路径长度不能大于1,000")
    private String supervisionClassUrl;

    /**
     * 监课开始时间(yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "监课开始时间(yyyy-MM-dd HH:mm:ss） 字典类型：supervision_class_start_time" ,type = "supervision_class_start_time")
    private LocalDateTime supervisionClassStartTime;

    /**
     * 监课结束时间(yyyy-MM-dd HH:mm:ss）
     */
    @Schema(description = "监课结束时间(yyyy-MM-dd HH:mm:ss） 字典类型：supervision_class_end_time" ,type = "supervision_class_end_time")
    private LocalDateTime supervisionClassEndTime;

    /**
     * 主讲老师ID(ss_lecturer主键ID)
     */
    @Schema(description = "主讲老师ID(ss_lecturer主键ID)")
    private Long lecturerId;

    /**
     * 主讲设备ID
     */
    @Schema(description = "主讲设备ID")
    private Long deviceId;

    /**
     * 主讲教室ID
     */
    @Schema(description = "主讲教室ID")
    private Long classRoomId;

    /**
     * 书籍ID
     */
    @Schema(description = "书籍ID")
    @Length(groups = {V_A_E.class }, max =255 ,message = "书籍ID长度不能大于255")
    private String booksId;

    /**
     * 书籍名称
     */
    @Schema(description = "书籍名称")
    @Length(groups = {V_A_E.class }, max =255 ,message = "书籍名称长度不能大于255")
    private String booksName;

    /**
     * 课程库ID(录播课资源ID)
     */
    @Schema(description = "课程库ID(录播课资源ID)")
    private Long recordingId;

    /**
     * 主讲端上课码(上课端标识1 + 5位随机数  例:115329)
     */
    @Schema(description = "主讲端上课码(上课端标识1 + 5位随机数  例:115329) 字典类型：lecturer_room_code" ,type = "lecturer_room_code")
    @Length(groups = {V_A_E.class }, max =10 ,message = "主讲端上课码(上课端标识1 + 5位随机数  例长度不能大于10")
    private String lecturerRoomCode;

    /**
     * 教室端上课码(教室端标识2 + 5位随机数  例:235329)
     */
    @Schema(description = "教室端上课码(教室端标识2 + 5位随机数  例:235329) 字典类型：class_room_code" ,type = "class_room_code")
    @Length(groups = {V_A_E.class }, max =10 ,message = "教室端上课码(教室端标识2 + 5位随机数  例长度不能大于10")
    private String classRoomCode;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime ctime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Length(groups = {V_A_E.class }, max =64 ,message = "创建者长度不能大于64")
    private String creator;

    /**
     * 编辑时间
     */
    @Schema(description = "编辑时间")
    private LocalDateTime mtime;

    /**
     * 编辑者
     */
    @Schema(description = "编辑者")
    @Length(groups = {V_A_E.class }, max =64 ,message = "编辑者长度不能大于64")
    private String modifer;

    /**
     * 删除标记
     */
    @Schema(description = "删除标记", defaultValue = "'0'")
    @NotNull(groups = {V_A_E.class }, message = "删除标记不能为空")
    private Integer delFlag;


}
