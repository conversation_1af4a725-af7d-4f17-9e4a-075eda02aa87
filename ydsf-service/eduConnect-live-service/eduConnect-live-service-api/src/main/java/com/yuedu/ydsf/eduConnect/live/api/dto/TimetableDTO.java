package com.yuedu.ydsf.eduConnect.live.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.Data;

/**
 * 门店课表 传输类
 *
 * <AUTHOR>
 * @date 2024-12-09 10:27:50
 */
@Data
@Schema(description = "门店课表传输类")
public class TimetableDTO implements Serializable {


    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Long storeId;

    /**
     * 指导老师ID
     */
    @Schema(description = "指导老师ID")
    private Long teacherId;

    /**
     * 班级ID
     */
    @Schema(description = "班级ID")
    private Long classId;

    /**
     * 教室ID
     */
    @Schema(description = "教室ID")
    private Long classroomId;


    /**
     * 门店已约直播课Id
     */
    @Schema(description = "门店已约直播课Id")
    private Long coursePlanId;

    /**
     * 上课时段ID
     */
    @Schema(description = "上课时段ID")
    private Long timeSlotId;

    /**
     * 时段类型: 1-上午; 2-下午; 3-晚上;
     */
    @Schema(description = "时段类型: 1-上午; 2-下午; 3-晚上;")
    private Integer timeSlotType;

    /**
     * 上课日期
     */
    @Schema(description = "上课日期")
    private LocalDate classDate;


    /**
     * 课程类型id
     */
    @Schema(description = "课程类型id")
    private Long courseTypeId;
}
