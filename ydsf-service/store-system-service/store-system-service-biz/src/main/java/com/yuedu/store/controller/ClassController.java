package com.yuedu.store.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.ClassDTO;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.query.ClassQuery;
import com.yuedu.store.service.ClassService;
import com.yuedu.store.valid.ClassStudentValidGroup;
import com.yuedu.store.vo.ClassVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.feign.annotation.NoToken;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 班级 控制类
 *
 * <AUTHOR>
 * @date 2024-11-26 14:30:59
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/class")
@Tag(description = "t_class", name = "班级管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ClassController {

    private final ClassService classService;

    /**
     * 通过校区ID查班级
     */
    @Operation(summary = "通过校区Id查询", description = "通过校区Id查询对象")
    @PostMapping("/getByCampusId")
    @Inner
    public R<List<ClassVO>> getByCampusId(@RequestBody CampusQuery campusQuery) {
        return R.ok(classService.getClassByCampusId(campusQuery));
    }

    /**
     * 通过校区ID查班级
     */
    @Operation(summary = "小程序:通过校区Id查询", description = "小程序：通过校区Id查询对象")
    @GetMapping("/getInfoByCampusId")
    @StorePermission
    public R<List<ClassVO>> getInfoByCampusId() {
        CampusQuery campusQuery = new CampusQuery();
        campusQuery.setCampusId(StoreContextHolder.getStoreId());
        return R.ok(classService.getClassByCampusId(campusQuery));
    }

    /**
     * 通过班级ID查询
     *
     * @param classIdList 班级ID集合
     * @return List<ClassVO>
     */
    @Operation(summary = "通过班级ID查询", description = "通过班级ID查询对象")
    @GetMapping("/getByIdList")
    @Inner
    public R<List<ClassVO>> getClassByIdList(@RequestParam List<Long> classIdList) {
        if (classIdList == null || classIdList.isEmpty()) {
            return R.ok();
        }
        return R.ok(BeanUtil.copyToList(classService.listByIds(classIdList), ClassVO.class));
    }



    /**
     *  通过班级ID集合查询
     *
     * <AUTHOR>
     * @date 2025年07月14日 14时28分
     */
    @PostMapping("/getByIdLists")
    @NoToken
    @Inner
    R<List<ClassVO>> getClassByIdLists(@RequestBody List<Long> classIdList){
        if (classIdList == null || classIdList.isEmpty()) {
            return R.ok();
        }
        return R.ok(BeanUtil.copyToList(classService.listByIds(classIdList), ClassVO.class));
    }


    /**
     * 通过班级ID查询
     *
     * @param classIdList 班级ID集合
     * @return List<ClassVO>
     */
    @Operation(summary = "通过班级ID查询", description = "通过班级ID查询对象")
    @PostMapping("/getClassByIdList")
    @Inner
    public R<List<ClassVO>> getByIdList(@RequestBody List<Long> classIdList) {
        if (classIdList == null || classIdList.isEmpty()) {
            return R.ok();
        }
        return R.ok(BeanUtil.copyToList(classService.listByIds(classIdList), ClassVO.class));
    }

    /**
     * 通过门店id获取班级列表分页
     * @param page    分页对象
     * @param classQuery 班级信息
     * @return R
     */
    @Operation(summary = "通过门店id获取班级列表分页", description = "通过门店id获取班级列表分页")
    @GetMapping("/page")
    @StorePermission
    public R allClassPage(@ParameterObject Page page, @ParameterObject ClassQuery classQuery) {
        return R.ok(classService.allClassPage(page, classQuery));
    }


    /**
     * 通过门店ID查班级
     */
    @Operation(summary = "小程序:通过门店Id查询", description = "小程序：通过门店Id查询对象")
    @GetMapping("/getInfoByStoreId")
    @StorePermission
    public R<List<ClassVO>> getInfoByStoreId(@ParameterObject ClassQuery classQuery) {
        classQuery.setCampusId(StoreContextHolder.getStoreId());
        return R.ok(classService.getClassByStoreId(classQuery));
    }

    /**
     * 创建班级
     *
     * @return R
     */
    @Operation(summary = "创建班级", description = "创建班级")
    @SysLog("创建班级")
    @PostMapping
    @StorePermission
    public R save(@Validated(ValidGroup.Insert.class)@RequestBody ClassDTO classDTO) {
        classDTO.setStoreId(StoreContextHolder.getStoreId());
        classDTO.setSchoolId(StoreContextHolder.getSchoolId());
        return R.ok(classService.saveClass(classDTO));
    }

    /**
     * 修改班级
     *
     * @return R
     */
    @Operation(summary = "修改班级", description = "修改班级")
    @SysLog("创建班级")
    @PutMapping
    @StorePermission
    public R update(@Validated(ValidGroup.Update.class)@RequestBody ClassDTO classDTO) {
        classDTO.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(classService.saveClass(classDTO));
    }

    /**
     * 班级结课
     *
     * @return R
     */
    @Operation(summary = "班级结课", description = "班级结课")
    @SysLog("班级结课")
    @PostMapping("/endClass")
    @StorePermission
    public R endClass(@Validated(ClassStudentValidGroup.EndClass.class)@RequestBody ClassDTO classDTO) {
        classDTO.setStoreId(StoreContextHolder.getStoreId());
        classService.endClass(classDTO);
        return R.ok();
    }

    /**
     * 查看班级详情
     *
     * @return R
     */
    @Operation(summary = "查看班级详情", description = "查看班级详情")
    @GetMapping("/getDetail")
    @StorePermission
    public R getDetail(@RequestParam Integer id) {
        return R.ok(classService.getDetail(id));
    }

    /**
     * 删除班级
     */
    @Operation(summary = "删除班级" , description = "删除班级" )
    @SysLog("删除班级" )
    @DeleteMapping("/delete")
    @StorePermission
    public R removeById(@Validated(ClassStudentValidGroup.DeleteClass.class)@RequestBody ClassDTO classDTO) {
        classDTO.setStoreId(StoreContextHolder.getStoreId());
        classService.removeClass(classDTO);
        return R.ok();
    }

}
