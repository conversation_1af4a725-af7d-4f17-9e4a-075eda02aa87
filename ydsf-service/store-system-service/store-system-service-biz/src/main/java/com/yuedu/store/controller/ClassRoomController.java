package com.yuedu.store.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.ClassRoomDTO;
import com.yuedu.store.entity.ClassRoomEntity;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.service.ClassRoomService;
import com.yuedu.store.valid.ClassRoomValidGroup;
import com.yuedu.store.vo.ClassRoomVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * 教室
 *
 * <AUTHOR>
 * @date 2024-10-08 16:11:43
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/classRoom")
@Tag(description = "classRoom", name = "教室管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ClassRoomController {

    private final ClassRoomService classRoomService;

    /**
     * 获取全部教室列表
     *
     * @return 教室列表
     */
    @Operation(summary = "查询全部教室", description = "查询全部教室")
    @GetMapping("/list")
    public R list(@RequestParam(name = "query", required = false, defaultValue = "") String name) {
        return R.ok(classRoomService.listClassRoom(name));
    }

    /**
     * 分页查询
     *
     * @param page      分页对象
     * @param classRoom 教室
     * @return 结果列表
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("store_classRoom_view")
    public R getClassRoomPage(@ParameterObject Page page, @ParameterObject ClassRoomEntity classRoom) {
        return R.ok(classRoomService.queryByCondition(page, classRoom));
    }


    /**
     * 通过条件查询教室
     *
     * @param classRoom 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/details")
    @HasPermission("store_classRoom_view")
    public R getDetails(@ParameterObject ClassRoomEntity classRoom) {
        return R.ok(classRoomService.list(Wrappers.query(classRoom)));
    }

    /**
     * 新增教室
     *
     * @param classRoom 教室
     * @return R
     */
    @Operation(summary = "新增教室", description = "新增教室")
    @SysLog("新增教室")
    @PostMapping
    @HasPermission("store_classRoom_add")
    public R save(@RequestBody ClassRoomEntity classRoom) {
        return R.ok(classRoomService.save(classRoom));
    }

    /**
     * 修改教室
     *
     * @param classRoom 教室
     * @return R
     */
    @Operation(summary = "修改教室", description = "修改教室")
    @SysLog("修改教室")
    @PutMapping
    @HasPermission("store_classRoom_edit")
    public R updateById(@RequestBody ClassRoomEntity classRoom) {
        return R.ok(classRoomService.updateById(classRoom));
    }

    /**
     * 通过id删除教室
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除教室", description = "通过id删除教室")
    @SysLog("通过id删除教室")
    @DeleteMapping
    @HasPermission("store_classRoom_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(classRoomService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 根据教室id集合获取所对应的教室列表
     *
     * @param classRoomDto 教室id集合
     * @return 教室实体集合
     */
    @Operation(summary = "根据ID集合查询教室列表", description = "根据ID集合查询教室列表")
    @PostMapping("/classRoomList")
    @Inner
    public R getClassRoomList(@RequestBody ClassRoomDTO classRoomDto) {
        return R.ok(classRoomService.queryList(classRoomDto));
    }


    /**
     * 根据教室id获取所对应的教室
     *
     * @param classRoomId 教室id
     * @return 教室实体
     */
    @Operation(summary = "根据教室ID查询教室", description = "根据教室ID查询教室")
    @GetMapping("/info/{id}")
    @Inner
    public R getClassRoom(@PathVariable("id") Long classRoomId) {
        return R.ok(classRoomService.queryByClassRoomId(classRoomId));
    }


    /**
     * 根据校区id查询教室
     *
     * @param campusId 校区id
     * @return 教室列表
     */
    @Operation(summary = "根据校区id查询教室", description = "根据校区id查询教室")
    @GetMapping("/infoByCampusId/{campusId}")
    public R getClassroomByCampusId(@PathVariable Long campusId) {
        return R.ok(classRoomService.queryList(campusId));
    }

    /**
     * 通过校区Id查询教室信息
     */
    @Operation(summary = "通过校区Id查询", description = "通过校区Id查询对象")
    @PostMapping("/getByCampusId")
    @Inner
    public R<List<ClassRoomVO>> getByCampusId(@RequestBody CampusQuery campusQuery) {
        return R.ok(classRoomService.getClassRoomByCampusId(campusQuery));
    }

    /**
     * 通过校区Id查询教室信息
     */
    @Operation(summary = "小程序:通过校区Id查询", description = "小程序:通过校区Id查询对象")
    @GetMapping("/getInfoByCampusId")
    @StorePermission
    public R<List<ClassRoomVO>> getInfoByCampusId() {
        CampusQuery campusQuery = new CampusQuery();
        campusQuery.setCampusId(StoreContextHolder.getStoreId());
        return R.ok(classRoomService.getClassRoomByCampusId(campusQuery));
    }

    /**
     * 通过ID集合获取教室Map
     *
     * @param classroomIdList idList
     * @return Map<Long, ClassRoomVO>
     */
    @Operation(summary = "通过ID集合获取教室Map", description = "通过ID集合获取教室Map")
    @PostMapping("/getMapByIdList")
    @Inner
    public R<Map<Long, ClassRoomVO>> getClassRoomMapByIdList(@RequestBody List<Long> classroomIdList) {
        return R.ok(classRoomService.getClassRoomMapByIdList(classroomIdList));
    }

    /**
     * 内部调用:查询所有教室
     * @param classRoomDto
     * @return com.yuedu.ydsf.common.core.util.R<java.util.List < com.yuedu.store.vo.ClassRoomVO>>
     * <AUTHOR>
     * @date 2025/2/10 16:36
     */
    @Operation(summary = "内部调用:查询所有教室", description = "内部调用:查询所有教室")
    @PostMapping("/getClassRoomAll")
    @Inner
    public R<List<ClassRoomVO>> getClassRoomAll(@RequestBody ClassRoomDTO classRoomDto) {
        return R.ok(classRoomService.getClassRoomList(classRoomDto));
    }

    /**
     * 内部调用:批量保存/更新教室
     * @param classRoomEntityList
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/2/10 11:39
     */
    @Operation(summary = "内部调用:批量保存/更新教室", description = "内部调用:批量保存/更新教室")
    @PostMapping("/saveOrUpdateBatchClassRoom")
    @Inner
    public R saveOrUpdateBatchClassRoom(@RequestBody List<ClassRoomEntity> classRoomEntityList) {
        classRoomService.saveOrUpdateBatch(classRoomEntityList);
        return R.ok();
    }


    /**
     * 店长端：增加教室
     * @param classRoomDTO classRoomDTO
     * @return Boolean
     */
    @Operation(summary = "店长端：增加教室", description = "店长端：增加教室")
    @PostMapping("/add")
    @StorePermission
    public R<Boolean> add(@Validated(ClassRoomValidGroup.Add.class) @RequestBody ClassRoomDTO classRoomDTO){
        return R.ok(classRoomService.addClassRoom(classRoomDTO));
    }

    /**
     * 店长端：修改教室
     * @param classRoomDTO classRoomDTO
     * @return Boolean
     */
    @Operation(summary = "店长端：修改教室", description = "店长端：修改教室")
    @PutMapping("/edit")
    @StorePermission
    public R<Boolean> edit(@Validated(ClassRoomValidGroup.Edit.class)@RequestBody ClassRoomDTO classRoomDTO){
        return R.ok(classRoomService.edit(classRoomDTO));
    }

    /**
     * 店长端：删除教室
     * @param classRoomDTO classRoomDTO
     * @return Boolean
     */
    @Operation(summary = "店长端：删除教室",description = "店长端：删除教室")
    @DeleteMapping("/del")
    @StorePermission
    public R<Boolean> del(@Validated(ClassRoomValidGroup.Delete.class) @RequestBody ClassRoomDTO classRoomDTO){
        return R.ok(classRoomService.removeClassRoom(classRoomDTO.getId()));
    }
}
