package com.yuedu.store.controller;

import com.yuedu.store.dto.ClassStudentDTO;
import com.yuedu.store.dto.ClassStudentMemberDTO;
import com.yuedu.store.service.ClassStudentService;
import com.yuedu.store.valid.ClassStudentValidGroup;
import com.yuedu.store.vo.ClassStudentVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 班级学生 控制类
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/classStudent" )
@Tag(description = "t_class_student" , name = "班级学生管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class ClassStudentController {

    private final ClassStudentService classStudentService;

    /**
     * 根据学生id获取班级列表
     * @param studentId 学员id
     */
    @Operation(summary = "根据学生id获取班级列表" , description = "根据学生id获取班级列表")
    @GetMapping("/classList")
    @Inner
    public R<List<ClassStudentVO>> getClassByStudentId(@RequestParam Integer studentId) {
        return R.ok(classStudentService.getClassList(studentId));
    }

    /**
     * 添加学员
     *
     * @return R
     */
    @Operation(summary = "添加学员", description = "添加学员")
    @SysLog("添加学员")
    @PostMapping
    @StorePermission
    public R save(@Validated(ValidGroup.Insert.class)@RequestBody ClassStudentDTO classStudentDTO) {
        classStudentDTO.setStoreId(StoreContextHolder.getStoreId());
        classStudentDTO.setSchoolId(StoreContextHolder.getSchoolId());
        classStudentService.saveStudent(classStudentDTO);
        return R.ok();
    }

    /**
     * 移除学员
     */
    @Operation(summary = "移除学员" , description = "移除学员" )
    @SysLog("移除学员" )
    @DeleteMapping("/delete")
    @StorePermission
    public R removeById(@Validated(ClassStudentValidGroup.DeleteClass.class)@RequestBody ClassStudentDTO classStudentDTO) {
        classStudentDTO.setStoreId(StoreContextHolder.getStoreId());
        classStudentService.removeStudent(classStudentDTO);
        return R.ok();
    }

    /**
     * 学员转班
     */
    @Operation(summary = "学员转班" , description = "学员转班" )
    @SysLog("学员转班" )
    @PostMapping("/changeClass")
    @StorePermission
    public R changeClass(@Validated(ClassStudentValidGroup.ChangeClass.class)@RequestBody  ClassStudentDTO classStudentDTO) {
        classStudentDTO.setStoreId(StoreContextHolder.getStoreId());
        classStudentDTO.setSchoolId(StoreContextHolder.getSchoolId());
        classStudentService.changeClass(classStudentDTO);
        return R.ok();
    }

    /**
     * 根据班级ID获取学生列表
     */
    @Operation(summary = "根据班级ID获取学生列表" , description = "根据班级ID获取学生列表" )
    @PostMapping("/inner/getStudentListByClassIdList")
    @Inner
    public R<List<ClassStudentMemberDTO>> getStudentListByClassIdList(@RequestBody List<Integer> classIdList) {
        return R.ok(classStudentService.getStudentListByClassIdList(classIdList));
    }
}
