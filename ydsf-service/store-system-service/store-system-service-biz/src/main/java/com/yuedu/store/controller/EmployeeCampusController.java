package com.yuedu.store.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.EmployeeCampusDTO;
import com.yuedu.store.dto.EmployeeCampusNewDTO;
import com.yuedu.store.entity.Employee;
import com.yuedu.store.query.AppUserQuery;
import com.yuedu.store.query.EmployeeCampusQuery;
import com.yuedu.store.service.EmployeeCampusService;
import com.yuedu.store.service.EmployeeService;
import com.yuedu.store.valid.AppUserQueryValidGroup;
import com.yuedu.store.valid.EmployeeCampusValidGroup;
import com.yuedu.store.vo.EmployeeCampusInfoVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.ValidGroup;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpHeaders;
/**
 * 员工 控制类
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/employee_campus")
@Tag(description = "t_employee_campus", name = "员工表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class EmployeeCampusController {

    private final EmployeeCampusService employeeCampusService;

    private final EmployeeService employeeService;

    /**
     * 查询全部员工
     *
     * @return 全部员工信息列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询全部员工", description = "查询全部员工")
    @StorePermission
    @SysLog("查询全部员工")
    public R<IPage<EmployeeCampusInfoVO>> list(@ParameterObject Page page, @ParameterObject EmployeeCampusQuery temployeeCampusQuery) {
        temployeeCampusQuery.setStoreId(StoreContextHolder.getStoreId());
        temployeeCampusQuery.setTerminal("STORE");
        return R.ok(employeeCampusService.page(page,temployeeCampusQuery));
    }

    /**
     * 查询校区员工
     *
     * @return 校区员工信息列表
     */
    @GetMapping("/schoolEmployeeCampusList")
    @Operation(summary = "查询校区员工", description = "查询校区员工")
    @StorePermission
    @SysLog("查询校区员工")
    public R<IPage<EmployeeCampusInfoVO>> schoolEmployeeCampusList(@ParameterObject Page page) {
        return R.ok(employeeCampusService.listSchoolEmployeeCampus(page));
    }

    /**
     * 通过主键查询员工详情
     * @apiNote 该接口用于通过员工ID查询员工详情信息。
     * @param id 员工ID，用于唯一标识一个员工。
     * @return 返回包含员工详情的响应对象R<EmployeeCampusInfoVO>，其中EmployeeCampusInfoVO封装了员工详情信息。
     */
    @Operation(description = "通过主键查询员工详情", summary = "通过主键查询员工详情")
    @GetMapping("/getById/{id}")
    @StorePermission
    @SysLog("通过主键查询员工详情")
    public R<EmployeeCampusInfoVO> getEmployeeCampusDetail(@PathVariable Long id) {
        return R.ok(employeeCampusService.getEmployeeCampusDetail(id));
    }

    /**
     * 更新员工离职/恢复入职状态
     * <p>通过HTTP POST请求更新员工的离职或恢复入职状态。</p>
     * @param employeeCampusDTO 包含要更新的员工信息的DTO对象，该对象应包含有效的员工ID和新的状态信息。
     */
    @Operation(description = "更新员工离职/恢复入职状态", summary = "更新员工离职/恢复入职状态")
    @PutMapping("/updateEmployeeCampusStatus")
    @StorePermission
    @SysLog("更新员工离职/恢复入职状态")
    public R updateEmployeeCampusStatus(@Validated({EmployeeCampusValidGroup.updateStatusValidGroup.class}) @RequestBody EmployeeCampusDTO employeeCampusDTO) {
        employeeCampusService.updateEmployeeCampusStatus(employeeCampusDTO);
        return R.ok();
    }

    /**
     * 更新员工信息接口
     * 该接口用于更新员工的校区信息。通过POST请求，将包含更新后员工信息的EmployeeCampusDTO对象作为请求体传递给服务器。
     * 服务器在处理请求时，会调用employeeCampusService的updateEmployeeCampus方法，根据EmployeeCampusDTO中的信息更新员工的校区信息。
     * 如果更新成功，服务器将返回表示成功的响应。
     *
     * @param employeeCampusDTO 包含更新后员工信息的DTO对象，该对象需要通过@Valid注解进行校验
     * @return R 类型的响应对象，如果更新成功则返回R.ok()，表示请求成功处理
     */
    @Operation(description = "更新员工信息", summary = "更新员工信息")
    @PutMapping("/updateEmployeeCampusInfo")
    @StorePermission
    @SysLog("更新员工信息")
    public R updateEmployeeCampus(@Validated({EmployeeCampusValidGroup.employeeCampusValidGroup.class}) @RequestBody EmployeeCampusDTO employeeCampusDTO) {
        employeeCampusService.updateEmployeeCampus(employeeCampusDTO);
        return R.ok();
    }

    /**
     * 新增员工信息接口
     * 该接口用于新增员工信息。通过POST请求，将包含员工信息的EmployeeCampusDTO对象作为请求体传递给服务器。
     * 服务器在处理请求时，会调用employeeCampusService的installEmployeeCampus方法，将员工信息保存到数据库中。
     * 如果操作成功，服务器将返回表示成功的响应。
     *
     * @param employeeCampusDTO 包含员工信息的DTO对象，该对象需要通过@Valid注解进行校验
     * @return R 类型的响应对象，如果操作成功则返回R.ok()，表示请求成功处理
     */
    @Operation(description = "新增员工信息", summary = "新增员工信息")
    @PostMapping("/installEmployeeCampus")
    @StorePermission
    @SysLog("新增员工信息")
    public R installEmployeeCampus(@Validated(V_A.class) @RequestBody EmployeeCampusDTO employeeCampusDTO) {
        employeeCampusService.installEmployeeCampus(employeeCampusDTO);
        return R.ok();
    }

    /**
     * 根据手机号查询老师
     */
    @Operation(summary = "根据手机号查询", description = "根据手机号查询对象")
    @GetMapping("/getByPhone")
    @StorePermission
    @SysLog("新增员工信息")
    public R<Employee> getByPhone(@ParameterObject
                                  @Validated({ AppUserQueryValidGroup.GetByPhone.class })
                                  AppUserQuery appUserQuery) {
        return R.ok(employeeService.getEmployeeByPhone(appUserQuery));
    }

    /**
     * 选择员工加入门店
     */
    @Operation(summary = "选择员工加入门店", description = "选择员工加入门店")
    @PostMapping("/saveIdsEmployeeCampus")
    @StorePermission
    @SysLog("选择员工加入门店")
    public R<Employee> saveIdsEmployeeCampus(@RequestBody  EmployeeCampusDTO employeeCampusDTO) {
        employeeCampusService.saveIdsEmployeeCampus(employeeCampusDTO);
        return R.ok();
    }
    // 校区用户管理接口

    /**
     * 用户管理分页查询
     *
     * @param page         分页对象
     * @param temployeeCampusQuery 用户信息表
     * @return
     */
    @Operation(summary = "校区用户管理分页查询", description = "用户管理分页查询")
    @GetMapping("/page")
    @HasPermission("store_employee_campus_page")
    @SysLog("新增用户信息表")
    public R<IPage<EmployeeCampusInfoVO>> getEmployeeCampusPage(@ParameterObject Page page, @ParameterObject EmployeeCampusQuery temployeeCampusQuery) {
        return R.ok(employeeCampusService.page(page, temployeeCampusQuery));
    }

    @Operation(summary = "用户编辑", description = "用户编辑")
    @PutMapping("/updateUser")
    @HasPermission("store_employee_campus_update")
    @SysLog("用户编辑")
    public R updateUser(@Validated(ValidGroup.Update.class) @RequestBody EmployeeCampusNewDTO params) {
        employeeCampusService.updateEmployeeCampus(params);
        return R.ok();
    }

    @Operation(summary = "用户管理禁用启用", description = "用户管理禁用启用")
    @PutMapping("/updateIsEnable")
    @HasPermission("store_employee_campus_is_enable")
    @SysLog("用户管理禁用启用")
    public R updateIsEnable(@Validated(EmployeeCampusValidGroup.IsEnable.class) @RequestBody EmployeeCampusNewDTO params) {
        employeeCampusService.updateIsEnable(params);
        return R.ok();
    }

    @Operation(summary = "用户新增", description = "用户新增")
    @PostMapping("/installUser")
    @HasPermission("store_employee_campus_install")
    @SysLog("用户新增")
    public R installUser(@Validated(ValidGroup.Insert.class) @RequestBody EmployeeCampusNewDTO params) {
        employeeCampusService.addEmployeeCampus(params);
        return R.ok();
    }
}
