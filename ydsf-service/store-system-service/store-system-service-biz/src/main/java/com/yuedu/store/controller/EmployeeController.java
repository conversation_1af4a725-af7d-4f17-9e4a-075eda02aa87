package com.yuedu.store.controller;

import com.github.houbb.heaven.util.lang.BeanUtil;
import com.yuedu.store.constant.enums.EmployeeCampusStatusEnum;
import com.yuedu.store.dto.StoreEmployeeDTO;
import com.yuedu.store.entity.Employee;
import com.yuedu.store.entity.EmployeeAttr;
import com.yuedu.store.query.AppUserQuery;
import com.yuedu.store.query.CampusQuery;
import com.yuedu.store.service.EmployeeAttrService;
import com.yuedu.store.service.EmployeeService;
import com.yuedu.store.valid.AppUserQueryValidGroup;
import com.yuedu.store.valid.StoreEmployeeAttrValidGroup;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.store.vo.StoreEmployeeAttrVO;
import com.yuedu.store.vo.StoreEmployeeVO;
import com.yuedu.ydsf.common.core.util.FileUtils;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 老师 控制类
 *
 * <AUTHOR>
 * @date 2024-11-26 15:09:08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/employee")
@Tag(description = "t_employee", name = "老师管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class EmployeeController {

    private final EmployeeService employeeService;

    private final EmployeeAttrService storeEmployeeAttrService;

    /**
     * 根据校区ID查询老师
     */
    @Operation(summary = "通过校区ID查询", description = "通过校区ID查询对象")
    @PostMapping("/getByCampusId")
    @Inner
    public R<List<EmployeeVO>> getByCampusId(@RequestBody CampusQuery campusQuery) {
        return R.ok(employeeService.getEmployeeByCampusId(campusQuery, null));
    }

    /**
     * 根据门店ID查询老师
     */
    @Operation(summary = "小程序：通过门店ID查询", description = "小程序：通过门店ID查询对象")
    @GetMapping("/getInfoByCampusId")
    @StorePermission
    public R<List<EmployeeVO>> getInfoByCampusId() {
        CampusQuery campusQuery = new CampusQuery();
        campusQuery.setCampusId(StoreContextHolder.getStoreId());
        //正常状态
        return R.ok(employeeService.getEmployeeByCampusId(campusQuery, EmployeeCampusStatusEnum.ACTIVE.getCode()));
    }

    /**
     * 根据手机号查询老师
     */
    @Operation(summary = "根据手机号查询", description = "根据手机号查询对象")
    @PostMapping("/getByPhone")
    @Inner
    public R<Employee> getByPhone(@RequestBody
                                  @Validated({AppUserQueryValidGroup.GetByPhone.class})
                                  AppUserQuery appUserQuery) {
        return R.ok(employeeService.getEmployeeByPhone(appUserQuery));
    }


    /**
     * 通过老师ID获取老师信息
     *
     * @param teacherIdList 老师ID集合
     * @return Map<Long, EmployeeVO>
     */
    @Operation(summary = "通过老师ID获取老师信息", description = "通过老师ID获取老师信息")
    @PostMapping("/getMapById")
    @Inner
    R<List<EmployeeVO>> getEmployeeMapByIdList(@RequestBody List<Long> teacherIdList) {
        return R.ok(employeeService.getEmployeeListByIdList(teacherIdList));
    }

    /**
     * 通过主键查询员工附属属性表
     *
     * @param id 主键
     * @return StoreEmployeeVO
     */
    @Operation(description = "通过主键查询员工附属属性表", summary = "通过主键查询员工附属属性表")
    @GetMapping("/getById/{id}")
    @HasPermission("store_storeEmployeeAttr_view")
    public R<StoreEmployeeAttrVO> getById(@PathVariable Long id) {
        StoreEmployeeAttrVO storeEmployeeAttrVO = new StoreEmployeeAttrVO();
        EmployeeAttr storeEmployeeAttr = storeEmployeeAttrService.getById(id);
        BeanUtil.copyProperties(storeEmployeeAttr, storeEmployeeAttrVO);
        Employee byId = employeeService.getById(id);
        storeEmployeeAttr.setPhoto(FileUtils.completeUrl(storeEmployeeAttr.getPhoto()));
        storeEmployeeAttrVO.setName(byId.getName());
        storeEmployeeAttrVO.setNickName(byId.getNickname());
        return R.ok(storeEmployeeAttrVO);
    }

    /**
     * 小程序:上传证件照/简介
     *
     * @param storeEmployeeDTO 员工附属属性表
     * @return Boolean
     */
    @PutMapping("/update")
    @Operation(description = "小程序:上传证件照/简介", summary = "小程序:上传证件照/简介")
    @StorePermission
    public R<Boolean> updateById(@Validated(StoreEmployeeAttrValidGroup.UpdateInfo.class) @RequestBody StoreEmployeeDTO storeEmployeeDTO) {
        storeEmployeeDTO.setUserId(SecurityUtils.getUser().getId());
        return R.ok(storeEmployeeAttrService.updateInfo(storeEmployeeDTO));
    }

    /**
     * 上传头像
     *
     * @param storeEmployeeDTO 员工附属属性表
     * @return Boolean
     */
    @Operation(description = "小程序：上传头像", summary = "小程序：上传头像")
    @PutMapping("/updateAvatar")
    @StorePermission
    public R<Boolean> updateAvatar(@Validated(StoreEmployeeAttrValidGroup.UpdateAvatar.class) @RequestBody StoreEmployeeDTO storeEmployeeDTO) {
        storeEmployeeDTO.setUserId(SecurityUtils.getUser().getId());
        return R.ok(storeEmployeeAttrService.updateAvatar(storeEmployeeDTO));
    }

    /**
     * 小程序:查询员工信息
     *
     * @return StoreEmployeeVO
     */
    @Operation(description = "小程序:查询员工信息", summary = "小程序:查询员工信息")
    @GetMapping("/getInfo")
    @StorePermission
    public R<StoreEmployeeVO> getById() {
        Long id = SecurityUtils.getUser().getId();
        return R.ok(storeEmployeeAttrService.getInfoById(id));
    }
}
