package com.yuedu.store.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.LecturerDTO;
import com.yuedu.store.entity.LecturerEntity;
import com.yuedu.store.service.LecturerService;
import com.yuedu.store.vo.LecturerVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 主讲老师表
 *
 * <AUTHOR>
 * @date 2024-10-14 09:06:32
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/lecturer")
@Tag(description = "ss_lecturer", name = "主讲老师表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class LecturerController {

    private final LecturerService lecturerService;

    /**
     * 查询全部主讲老师
     *
     * @return 全部主讲老师信息列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询全部主讲老师", description = "查询全部主讲老师")
    public R list(@RequestParam(name = "query", required = false, defaultValue = "") String name) {
        return R.ok(lecturerService.listLecturer(name));
    }

    /**
     * 根据教师Id查询教师信息
     *
     * @param teacherId 教师id,非校管家id
     * @return 主讲老师信息
     */
    @GetMapping("/info/{teacherId}")
    @Operation(summary = "根据教师Id查询教师信息", description = "根据教师Id查询教师信息")
    @Inner
    public R getLecture(@PathVariable Long teacherId) {
        return R.ok(lecturerService.selectByLecturerId(teacherId));
    }


    /**
     * 内部使用：分页查询
     *
     * @param lecturerDTO 讲师信息
     * @return 结果
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/getPageList")
    @Inner
    public R getPageList(@RequestBody LecturerDTO lecturerDTO) {
        LambdaQueryWrapper<LecturerEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(lecturerDTO.getLecturerName() != null, LecturerEntity::getLecturerName, lecturerDTO.getLecturerName());
        wrapper.eq(lecturerDTO.getId() != null, LecturerEntity::getId, lecturerDTO.getId());
        return R.ok(lecturerService.page(lecturerDTO.getPage(), wrapper));
    }


    /**
     * 根据教师Id列表查询教师信息列表
     *
     * @param lecturerDTO 教师idList
     * @return 主讲老师信息列表
     */
    @PostMapping("/lecturerList")
    @Operation(summary = "根据教师Id列表查询教师信息列表", description = "根据教师Id列表查询教师信息列表")
    @Inner
    public R getLectureList(@RequestBody @Valid LecturerDTO lecturerDTO) {
        return R.ok(lecturerService.selectListByLecturerId(lecturerDTO.getIds(), lecturerDTO.getLecturerName()));
    }

    /**
     * 分页查询
     *
     * @param page     分页对象
     * @param lecturer 主讲老师表
     * @return 主讲老师分页列表
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("store_lecturer_view")
    public R getLecturerPage(@ParameterObject Page page, @ParameterObject LecturerEntity lecturer) {
        LambdaQueryWrapper<LecturerEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StrUtil.isNotBlank(lecturer.getLecturerName()), LecturerEntity::getLecturerName, lecturer.getLecturerName());
        return R.ok(lecturerService.page(page, wrapper));
    }

    /**
     * 根据主讲老师姓名模糊查询主讲老师信息
     *
     * @param lecturerName 主讲老师姓名
     * @return 主讲老师信息列表
     */
    @Operation(summary = "根据主讲老师姓名模糊查询主讲老师信息", description = "根据主讲老师姓名模糊查询主讲老师信息")
    @GetMapping("/listByLecturerName")
    @Inner
    public R<List<LecturerVO>> listLecturersByName(@RequestParam String lecturerName) {
        return R.ok(lecturerService.selectByLecturer(lecturerName));
    }

    /**
     * 内部调用:查询所有主讲老师
     * @param lecturerDTO
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/2/10 16:46
     */
    @PostMapping("/getLecturerAll")
    @Operation(summary = "内部调用:查询所有主讲老师", description = "查询所有主讲老师")
    @Inner
    public R<List<LecturerVO>> getLecturerAll(@RequestBody @Valid LecturerDTO lecturerDTO) {
        return R.ok(lecturerService.getLecturerAll(lecturerDTO));
    }

    /**
     * 内部调用:批量保存/更新主讲老师
     * @param lecturerEntityList
     * @return com.yuedu.ydsf.common.core.util.R
     * <AUTHOR>
     * @date 2025/2/10 16:56
     */
    @Operation(summary = "内部调用:批量保存/更新主讲老师", description = "内部调用:批量保存/更新主讲老师")
    @PostMapping("/saveOrUpdateBatchLecturer")
    @Inner
    public R saveOrUpdateBatchLecturer(@RequestBody List<LecturerEntity> lecturerEntityList) {
        lecturerService.saveOrUpdateBatch(lecturerEntityList);
        return R.ok();
    }

}