/*
 *
 *      Copyright (c) 2018-2025, ydsf All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: ydsf
 *
 */

package com.yuedu.store.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.entity.Role;
import com.yuedu.store.service.RoleService;
import com.yuedu.ydsf.admin.api.vo.RoleMenuVO;
import com.yuedu.ydsf.common.core.constant.CacheConstants;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/role")
@Tag(description = "role", name = "角色管理模块")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class RoleController {

    private final RoleService roleService;

    /**
     * 通过ID查询角色信息
     *
     * @param id ID
     * @return 角色信息
     */
    @GetMapping("/details/{id}")
    public R getById(@PathVariable Long id) {
        return R.ok(roleService.getById(id));
    }

    /**
     * 查询角色信息
     *
     * @param query 查询条件
     * @return 角色信息
     */
    @GetMapping("/details")
    public R getDetails(@ParameterObject Role query) {
        return R.ok(roleService.getOne(Wrappers.query(query), false));
    }

    /**
     * 添加角色
     *
     * @param sysRole 角色信息
     * @return success、false
     */
    @SysLog("添加角色")
    @PostMapping
//    @HasPermission("store_role_add")
    @CacheEvict(value = CacheConstants.ROLE_DETAILS, allEntries = true)
    public R save(@Valid @RequestBody Role sysRole) {
        return R.ok(roleService.save(sysRole));
    }

    /**
     * 修改角色
     *
     * @param sysRole 角色信息
     * @return success/false
     */
    @SysLog("修改角色")
    @PutMapping
//    @HasPermission("store_role_edit")
    @CacheEvict(value = CacheConstants.ROLE_DETAILS, allEntries = true)
    public R update(@Valid @RequestBody Role sysRole) {
        return R.ok(roleService.updateById(sysRole));
    }

    /**
     * 删除角色
     *
     * @param ids
     * @return
     */
    @SysLog("删除角色")
    @DeleteMapping
//    @HasPermission("store_role_del")
    @CacheEvict(value =  "store-"+CacheConstants.ROLE_DETAILS, allEntries = true)
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(roleService.removeRoleByIds(ids));
    }

    /**
     * 获取角色列表
     *
     * @return 角色列表
     */
    @GetMapping("/list")
    public R listRoles() {
        return R.ok(roleService.list(Wrappers.emptyWrapper()));
    }

    /**
     * 分页查询角色信息
     *
     * @param page 分页对象
     * @param role 查询条件
     * @return 分页对象
     */
    @GetMapping("/page")
    public R getRolePage(Page page, Role role) {
        return R.ok(roleService.page(page, Wrappers.<Role>lambdaQuery()
                .like(StrUtil.isNotBlank(role.getRoleName()), Role::getRoleName, role.getRoleName())));
    }

    /**
     * 更新角色菜单
     *
     * @param roleVo 角色对象
     * @return success、false
     */
    @SysLog("更新角色菜单")
    @PutMapping("/menu")
//    @HasPermission("store_role_perm")
    public R saveRoleMenus(@RequestBody RoleMenuVO roleVo) {
        return R.ok(roleService.updateRoleMenus(roleVo));
    }

    /**
     * 通过角色ID 查询角色列表
     *
     * @param roleIdList 角色ID
     * @return
     */
    @PostMapping("/getRoleList")
    public R getRoleList(@RequestBody List<Long> roleIdList) {
        return R.ok(roleService.findRolesByRoleIds(roleIdList, CollUtil.join(roleIdList, StrUtil.UNDERLINE)));
    }

}
