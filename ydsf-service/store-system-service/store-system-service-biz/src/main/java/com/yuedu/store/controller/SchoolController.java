package com.yuedu.store.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.SchoolDTO;
import com.yuedu.store.entity.School;
import com.yuedu.store.query.SchoolQuery;
import com.yuedu.store.service.SchoolService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 校区
 *
 * <AUTHOR>
 * @date 2025-02-07 08:34:23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/school")
@Tag(description = "school", name = "校区管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SchoolController {

    private final SchoolService schoolService;

    /**
     * 分页查询
     *
     * @param page   分页对象
     * @param schoolQuery 校区
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @HasPermission("store_school_view")
    public R getStoreSchoolPage(@ParameterObject Page page, @ParameterObject SchoolQuery schoolQuery) {
        return R.ok(schoolService.pageQuery(page, schoolQuery));
    }

    /**
     * 新增校区
     *
     * @param school 校区
     * @return R
     */
    @Operation(summary = "新增校区", description = "新增校区")
    @SysLog("新增校区")
    @PostMapping
    @HasPermission("store_school_add")
    public R save(@Valid @RequestBody SchoolDTO school) {
        schoolService.addSchool(school);
        return R.ok();
    }

    /**
     * 修改校区
     *
     * @param school 校区
     * @return R
     */
    @Operation(summary = "修改校区", description = "修改校区")
    @SysLog("修改校区")
    @PutMapping
    @HasPermission("store_school_edit")
    public R updateById(@Valid @RequestBody SchoolDTO school) {
        schoolService.editSchool(school);
        return R.ok();
    }

    /**
     * 通过id查询校区表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}")
    public R<School> getById(@PathVariable("id" ) Long id) {
        return R.ok(schoolService.getById(id));
    }
}