package com.yuedu.store.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.StoreCourseHoursLogDTO;
import com.yuedu.store.dto.StudentCourseHoursLogDTO;
import com.yuedu.store.entity.StoreCourseHoursLog;
import com.yuedu.store.query.CourseHoursLogQuery;
import com.yuedu.store.service.StoreCourseHoursLogService;
import com.yuedu.store.service.StudentService;
import com.yuedu.store.vo.CourseHoursLogVO;
import com.yuedu.store.vo.StoreCourseHoursLogVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 课时购买日志表
 *
 * <AUTHOR>
 * @date 2025-02-13 14:22:18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/storeCourseHoursLog")
@Tag(description = "storeCourseHoursLog", name = "课时购买日志表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class StoreCourseHoursLogController {

    @Resource
    private StoreCourseHoursLogService storeCourseHoursLogService;
    @Value("${ss.config.js-system.token}")
    private String ssToken;
    @Autowired
    private StudentService studentService;


    /**
     * 通过学生id查询课次记录
     *
     * @param page      分页参数
     * @param studentId 学生id
     * @return Page<CourseHoursLogVO>
     */
    @GetMapping("/getByStudentId")
    @StorePermission
    public R<Page<CourseHoursLogVO>> getByStudentId(@ParameterObject Page<StoreCourseHoursLog> page, @NotNull Long studentId) {
        return R.ok(storeCourseHoursLogService.getByStudentId(page, studentId));
    }


    /**
     * 更加课次获得课消信息
     *
     * <AUTHOR>
     * @date 2025年03月11日 16时52分
     */
    @PostMapping("/getStudentConsumeListByIds")
    @Inner
    R<List<StoreCourseHoursLogVO>> getStudentConsumeListByIds(@RequestBody List<Long> ids) {
        return R.ok(storeCourseHoursLogService.getStudentConsumeListByIds(ids));
    }

    /**
     * 结算系统查询课消记录
     */
    @PostMapping("/listCourseHoursLog")
    @Inner
    public R<List<StoreCourseHoursLogDTO>> listCourseHoursLog(@Validated @NotNull @RequestBody CourseHoursLogQuery courseHoursLogQuery) {
        return R.ok(storeCourseHoursLogService.listCourseHoursLog(courseHoursLogQuery));
    }

    /**
     * 根据飞天用户ID查询课消记录
     */
    @GetMapping("/getByFtUserId")
    @Inner(value = false)
    public R<StudentCourseHoursLogDTO> getByFtUserId(@RequestParam("ftUserId") Long ftUserId, HttpServletRequest request) {
        String token = request.getHeader("token");
            if (token == null || !token.equals(ssToken)) {
            return R.ok("无权限访问");
        }
        return R.ok(storeCourseHoursLogService.getByFtUserId(ftUserId));
    }
}
