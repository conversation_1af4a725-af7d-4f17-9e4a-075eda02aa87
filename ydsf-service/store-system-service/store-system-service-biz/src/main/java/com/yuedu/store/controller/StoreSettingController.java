package com.yuedu.store.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.yuedu.store.constant.cst.StoreConstant;
import com.yuedu.store.dto.StoreSettingDTO;
import com.yuedu.store.entity.CampusEntity;
import com.yuedu.store.entity.StoreSetting;
import com.yuedu.store.query.StoreSettingQuery;
import com.yuedu.store.service.CampusService;
import com.yuedu.store.service.StoreSettingService;
import com.yuedu.store.vo.StoreSettingVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 门店属性设置表 控制类
 *
 * <AUTHOR>
 * @date 2025-07-08 09:43:29
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/storeSetting")
@Tag(description = "store_setting", name = "门店属性设置表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class StoreSettingController {

    private final StoreSettingService storeSettingService;
    private final CampusService campusService;
    @Value("${ss.config.js-system.token}")
    private String ssToken;

    /**
     * 分页查询
     *
     * @param storeSettingQuery 门店属性设置表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/inner/page")
    @Inner
    public R<PageDTO<StoreSettingDTO>> getStoreSettingPage(@RequestBody StoreSettingQuery storeSettingQuery) {
        LambdaQueryWrapper<StoreSetting> wrapper = Wrappers.lambdaQuery();
        if (Objects.nonNull(storeSettingQuery)) {
            wrapper.eq(Objects.nonNull(storeSettingQuery.getStoreId()), StoreSetting::getStoreId, storeSettingQuery.getStoreId())
                    .eq(StringUtils.isNotEmpty(storeSettingQuery.getSettingKey()),
                            StoreSetting::getSettingKey, storeSettingQuery.getSettingKey());
        }
        Page<StoreSetting> page = new Page<>(storeSettingQuery.getCurrent(), storeSettingQuery.getSize());
        Page<StoreSetting> storeSettingPage = storeSettingService.page(page, wrapper);
        List<StoreSettingDTO> storeSettingDTOList = storeSettingPage.getRecords()
                .stream()
                .map(storeSetting -> {
                    StoreSettingDTO storeSettingDTO = new StoreSettingDTO();
                    BeanUtils.copyProperties(storeSetting, storeSettingDTO);
                    return storeSettingDTO;
                }).toList();
        PageDTO<StoreSettingDTO> pageDTO = new PageDTO<>(storeSettingPage.getCurrent(), storeSettingPage.getSize(), storeSettingPage.getTotal());
        pageDTO.setRecords(storeSettingDTOList);
        return R.ok(pageDTO);
    }


    /**
     * 通过条件查询门店属性设置表
     *
     * @param storeSetting 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/details")
    @HasPermission("storeSetting_view")
    public R getDetails(@ParameterObject StoreSetting storeSetting) {
        return R.ok(storeSettingService.list(Wrappers.query(storeSetting)));
    }

    /**
     * 新增门店属性设置表
     *
     * @param storeSetting 门店属性设置表
     * @return R
     */
    @Operation(summary = "新增门店属性设置表", description = "新增门店属性设置表")
    @SysLog("新增门店属性设置表")
    @PostMapping("/add")
    @HasPermission("storeSetting_add")
    public R save(@RequestBody StoreSetting storeSetting) {
        return R.ok(storeSettingService.save(storeSetting));
    }

    /**
     * 修改门店属性设置表
     *
     * @param storeSetting 门店属性设置表
     * @return R
     */
    @Operation(summary = "修改门店属性设置表", description = "修改门店属性设置表")
    @SysLog("修改门店属性设置表")
    @PutMapping("/edit")
    @HasPermission("storeSetting_edit")
    public R updateById(@RequestBody StoreSetting storeSetting) {
        return R.ok(storeSettingService.updateById(storeSetting));
    }

    /**
     * 通过id删除门店属性设置表
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除门店属性设置表", description = "通过id删除门店属性设置表")
    @SysLog("通过id删除门店属性设置表")
    @DeleteMapping("/delete")
    @HasPermission("storeSetting_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(storeSettingService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 门店通过约读领航小程序查询所有家长服务设置
     */
    @Operation(summary = "查询家长服务设置", description = "查询家长服务设置")
    @GetMapping("/app/parentServiceSettings")
    @StorePermission
    public R<List<StoreSettingVO>> getParentServiceSettings() {
        Long storeId = StoreContextHolder.getStoreId();
        if (Objects.isNull(storeId)) {
            return R.failed("门店ID不能为空");
        }
        List<StoreSettingVO> storeSettingVOList = storeSettingService.getParentServiceSettings(storeId)
                .stream()
                .map(storeSetting -> {
                    StoreSettingVO storeSettingVO = new StoreSettingVO();
                    BeanUtils.copyProperties(storeSetting, storeSettingVO);
                    return storeSettingVO;
                }).sorted(Comparator.comparing(StoreSettingVO::getSettingKey)).toList();
        return R.ok(storeSettingVOList);
    }

    /**
     * 门店设置家长服务配置
     */
    @Operation(summary = "设置家长服务配置", description = "设置家长服务配置")
    @PostMapping("/app/setParentServiceSettings")
    @StorePermission
    @Idempotent(key = "'setParentServiceSettings_' + #storeSettingVO.storeId", expireTime = 3)
    public R setParentServiceSettings(@RequestBody StoreSettingVO storeSettingVO) {
        Long storeId = StoreContextHolder.getStoreId();
        if (Objects.isNull(storeId)) {
            return R.failed("门店ID不能为空");
        } else if (!storeId.equals(storeSettingVO.getStoreId())) {
            return R.failed("门店ID不匹配");
        }
        storeSettingVO.setStoreId(storeId);
        StoreSetting storeSetting = new StoreSetting();
        BeanUtils.copyProperties(storeSettingVO, storeSetting);
        storeSettingService.setParentServiceSettings(storeSetting);
        return R.ok();
    }

    /**
     * 通过条件查询门店属性设置表
     *
     * @param storeId    查询条件
     * @param settingKey 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询", description = "通过条件查询对象")
    @GetMapping("/inner/detailBySettingKey")
    @Inner
    public R<StoreSettingDTO> getDetailBySettingKey(@RequestParam Long storeId, @RequestParam String settingKey) {
        Optional<StoreSetting> storeSettingOptional = storeSettingService.getParentServiceSettings(storeId)
                .stream()
                .filter(storeSetting -> storeSetting.getSettingKey().equals(settingKey))
                .findFirst();
        if (storeSettingOptional.isEmpty()) {
            return R.failed("未找到对应的设置项");
        } else {
            StoreSettingDTO storeSettingDTO = new StoreSettingDTO();
            BeanUtils.copyProperties(storeSettingOptional.get(), storeSettingDTO);
            return R.ok(storeSettingDTO);
        }
    }

    /**
     * 查询门店的家长服务考勤通知设置是否开启
     *
     * @param storeId 门店ID
     * @return R<Boolean> true-开启，false-关闭
     */
    @Operation(summary = "查询门店考勤通知设置", description = "查询门店的家长服务考勤通知设置是否开启")
    @GetMapping("/inner/isAttendanceNoticeEnabled")
    @Inner
    public R<Boolean> isAttendanceNoticeEnabled(@RequestParam Long storeId) {
        if (storeId == null) {
            return R.failed("门店ID不能为空");
        }
        boolean enabled = storeSettingService.isParentServiceAttendanceNoticeEnabled(storeId);
        return R.ok(enabled);
    }


    /**
     * 查询门店的家长服务考勤通知设置是否开启
     *
     * @return R<Boolean> true-开启，false-关闭
     */
    @Operation(summary = "查询门店考勤通知设置", description = "查询门店的家长服务考勤通知设置是否开启")
    @GetMapping("/isAttendanceNoticeEnabled")
    @StorePermission
    public R<Boolean> isAttendanceNoticeEnabledByStore() {
        Long storeId = StoreContextHolder.getStoreId();
        boolean enabled = storeSettingService.isParentServiceAttendanceNoticeEnabled(storeId);
        return R.ok(enabled);
    }

    /**
     * 根据飞天门店ID查询门店课消属性设置
     */
    @Operation(summary = "根据飞天门店ID查询门店属性设置", description = "根据飞天门店ID查询门店属性设置")
    @GetMapping("/inner/getIsOpenKxBySchCode")
    @Inner(value = false)
    public R<Boolean> getBySchCode(@RequestParam String schCode, HttpServletRequest request) {
        if (StringUtils.isEmpty(schCode)) {
            return R.ok(false);
        }
        String token = request.getHeader("token");
        if (token == null || !token.equals(ssToken)) {
            return R.ok(false);
        }
        Wrapper<CampusEntity> queryWrapper = Wrappers.lambdaQuery(CampusEntity.class)
                .eq(CampusEntity::getCampusNo, schCode);
        Optional<CampusEntity> campusEntity = campusService.list(queryWrapper)
                .stream()
                .findFirst();
        if (campusEntity.isPresent()) {
            List<StoreSetting> storeSettings = storeSettingService.list(Wrappers.lambdaQuery(StoreSetting.class)
                    .eq(StoreSetting::getStoreId, campusEntity.get().getId())
                    .eq(StoreSetting::getSettingKey, StoreConstant.PARENT_SERVICE_REMAINING_COURSES));
            if (CollUtil.isEmpty(storeSettings)) {
                return R.ok(false);
            } else {
                StoreSetting storeSetting = storeSettings.get(0);
                if (StringUtils.isEmpty(storeSetting.getSettingValue())) {
                    return R.ok(false);
                }
                // 如果设置值为"1"，表示开启了家长服务
                if ("1".equals(storeSetting.getSettingValue())) {
                    return R.ok(true);
                } else {
                    return R.ok(false);
                }

            }
        } else {
            return R.ok(false);
        }
    }
}
