package com.yuedu.store.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.core.util.V_A;
import com.yuedu.ydsf.common.core.util.V_E;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import com.yuedu.store.service.StoreStudentTrackRecordService;
import com.yuedu.store.query.StoreStudentTrackRecordQuery;
import com.yuedu.store.dto.StoreStudentTrackRecordDTO;
import com.yuedu.store.vo.StoreStudentTrackRecordVO;

import java.io.Serializable;
import java.util.List;

/**
 * 会员跟踪记录控制层
 *
 * <AUTHOR>
 * @date 2025/06/25
 */

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/storeStudentTrackRecord")
@Tag(description = "store_student_track_record", name = "会员跟踪记录")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class StoreStudentTrackRecordController {


    private final StoreStudentTrackRecordService storeStudentTrackRecordService;


    /**
     * 会员跟踪记录分页查询
     *
     * @param page                         分页对象
     * @param storeStudentTrackRecordQuery 会员跟踪记录
     * @return R
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询", description = "会员跟踪记录分页查询")
    public R page(@ParameterObject Page page, @ParameterObject StoreStudentTrackRecordQuery storeStudentTrackRecordQuery) {
        return R.ok(storeStudentTrackRecordService.page(page, storeStudentTrackRecordQuery));
    }


    /**
     * 通过id查询会员跟踪记录
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询会员跟踪记录")
    @GetMapping("/{id}")
    public R getById(@PathVariable Serializable id) {
        return R.ok(storeStudentTrackRecordService.getById(id));
    }


    /**
     * 新增会员跟踪记录
     *
     * @param storeStudentTrackRecordDTO 会员跟踪记录
     * @return R
     */
    @PostMapping
    @SysLog("新增会员跟踪记录")
    @Operation(summary = "新增会员跟踪记录", description = "新增会员跟踪记录")
    @StorePermission
    public R add(@RequestBody StoreStudentTrackRecordDTO storeStudentTrackRecordDTO) {
        return R.ok(storeStudentTrackRecordService.add(storeStudentTrackRecordDTO));
    }


    /**
     * 修改会员跟踪记录
     *
     * @param storeStudentTrackRecordDTO 会员跟踪记录
     * @return R
     */
    @PutMapping
    @SysLog("修改会员跟踪记录")
    @Operation(summary = "修改会员跟踪记录", description = "修改会员跟踪记录")
    @StorePermission
    public R edit(@RequestBody StoreStudentTrackRecordDTO storeStudentTrackRecordDTO) {
        return R.ok(storeStudentTrackRecordService.edit(storeStudentTrackRecordDTO));
    }


    /**
     * 通过id删除会员跟踪记录
     *
     * @param ids id列表
     * @return R
     */
    @DeleteMapping
    @SysLog("通过id删除会员跟踪记录")
    @Operation(summary = "删除会员跟踪记录", description = "删除会员跟踪记录")
    public R delete(@RequestBody Long[] ids) {
        return R.ok(storeStudentTrackRecordService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 会员跟踪记录表格
     *
     * @param storeStudentTrackRecordQuery 查询条件
     * @param ids                          导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @Operation(summary = "导出会员跟踪记录表格", description = "导出会员跟踪记录表格")
    public List<StoreStudentTrackRecordVO> export(StoreStudentTrackRecordQuery storeStudentTrackRecordQuery, Long[] ids) {
        return storeStudentTrackRecordService.export(storeStudentTrackRecordQuery, ids);
    }


}
