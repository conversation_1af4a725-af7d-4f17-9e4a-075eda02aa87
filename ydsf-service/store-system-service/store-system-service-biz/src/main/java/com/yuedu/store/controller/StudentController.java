package com.yuedu.store.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.*;
import com.yuedu.store.entity.Student;
import com.yuedu.store.query.IntentionStudentQuery;
import com.yuedu.store.query.StudentQuery;
import com.yuedu.store.query.StudentQueryDTO;
import com.yuedu.store.service.CourseHoursPayService;
import com.yuedu.store.service.StudentService;
import com.yuedu.store.valid.StudentValidGroup;
import com.yuedu.store.vo.*;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.idempotent.annotation.Idempotent;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName StudentController
 * @Description 学员控制类
 * <AUTHOR>
 * @Date 2025/02/10 09:43
 * @Version v0.0.1
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/student")
@Tag(description = "t_student", name = "学员管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class StudentController {

    private final StudentService studentService;

    private final CourseHoursPayService courseHoursPayService;

    /**
     * 双师后台使用:多条件分页查询学员列表
     * 无需StorePermission注解
     *
     * @param page         分页对象
     * @param studentQuery 学员信息表
     * @return R
     */
    @Operation(summary = "多条件分页查询全部学员列表", description = "多条件分页查询学员列表")
    @GetMapping("/page")
    public R allStudentPage(@ParameterObject Page<Student> page, @ParameterObject StudentQuery studentQuery) {
        log.info("双师后台多条件分页查询全部学员列表的参数的参数:{}", JSON.toJSONString(studentQuery));
        return R.ok(studentService.allStudentPage(page, studentQuery));
    }



    /**
     * 学员课消
     *
     * @param ridStudent 学员课消
     * @return 结果
     */
    @Operation(summary = "学员课消", description = "学员课消")
    @PostMapping("/courseHoursRid")
    @Inner
    public R courseHoursRid(@RequestBody @Valid CourseHoursRidDTO ridStudent) {
        courseHoursPayService.reduction(ridStudent);
        return R.ok();
    }


    /**
     * 取消出勤
     *
     * @param params 取消出勤
     * @return 结果
     */
    @Operation(summary = "取消出勤", description = "取消出勤")
    @PostMapping("/courseHoursCancel")
    @Inner
    public R cancel(@RequestBody @Valid CourseHoursCancelDTO params) {
        courseHoursPayService.courseHoursCancel(params);
        return R.ok();
    }

    /**
     * 根据班级id获取学员列表
     *
     * @param classId 班级id
     * @return 学员列表
     */
    @Operation(summary = "根据班级id获取学员列表", description = "根据班级id获取学员列表")
    @GetMapping("/getByClassId")
    @Inner
    public R<List<StudentVO>> getStudentByClassId(@RequestParam List<Integer> classId, @RequestParam String condition) {
        return R.ok(studentService.getStudentByClassId(classId, condition));
    }

    /**
     * 获取学员列表
     *
     * @param classId 班级id
     * @return List<StudentVO>
     */
    @Operation(summary = "根据班级id获取学员列表", description = "根据班级id获取学员列表")
    @GetMapping("/innerList")
    @Inner
    public R<List<StudentVO>> getStudentByClassId(@RequestParam Integer classId) {
        return R.ok(studentService.getList(classId));
    }

    /**
     * 获取学员列表
     *
     * @param page            分页参数
     * @param studentQueryDTO 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    @GetMapping("/getStudentList")
    @StorePermission
    public R<Page<StudentVO>> getStudentList(@ParameterObject Page<Student> page, @ParameterObject StudentQueryDTO studentQueryDTO) {
        studentQueryDTO.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(studentService.getStudentList(page, studentQueryDTO));
    }

    /**
     * 根据门店id获取试听学员列表
     *
     * @param page         分页参数
     * @param studentQuery 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    @GetMapping("/getListByStoreId")
    @StorePermission
    public R<Page<StudentVO>> getListByStoreId(@ParameterObject Page<Student> page, @ParameterObject StudentQuery studentQuery) {
        studentQuery.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(studentService.getListByStoreId(page, studentQuery));
    }

    /**
     * 根据门店id获取试听且剩余课次为0的学员列表
     *
     * @param page         分页参数
     * @param studentQuery 班级id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    @GetMapping("/getStudentsWithTrial")
    @StorePermission
    @Operation(summary = "根据门店id获取试听且剩余课次为0的学员列表", description = "根据门店id获取试听且剩余课次为0的学员列表")
    public R<Page<StudentVO>> getTrialStudentsWithZeroCourseHours(@ParameterObject Page<Student> page, @ParameterObject StudentQuery studentQuery) {
        studentQuery.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(studentService.getTrialStudentsWithZeroCourseHours(page, studentQuery));
    }

    /**
     * 根据校区ID获取学生列表
     *
     * @param page         分页参数
     * @param studentQuery 校区id，姓名/手机号，阶段id
     * @return Page<StudentVO>
     */
    @GetMapping("/getListBySchoolId")
    @StorePermission
    @Operation(summary = "根据校区ID获取学生列表", description = "根据校区ID获取学生列表")
    public R<Page<StudentVO>> getListBySchoolId(@ParameterObject Page<Student> page, @ParameterObject StudentQuery studentQuery) {
        studentQuery.setSchoolId(StoreContextHolder.getSchoolId());
        return R.ok(studentService.getListBySchoolId(page, studentQuery));
    }


    /**
     *  根据学生ID获取学生列表
     *
     * <AUTHOR>
     * @date 2025年03月12日 10时54分
     */
    @PostMapping("/getStudentListByIds")
    @Operation(summary = "根据学生ID获取学生列表", description = "根据学生ID获取学生列表")
    @Inner
    R<List<StudentVO>> getStudentListByIds(@RequestBody List<Long> ids){
        return R.ok(studentService.getStudentListByIds(ids));
    }



    /**
     *  补课获取学生列表
     *
     * <AUTHOR>
     * @date 2025年03月12日 10时54分
     */
    @PostMapping("/getStudentListByMakeup")
    @Operation(summary = "根据学生ID获取学生列表", description = "根据学生ID获取学生列表")
    @Inner
    R<List<StudentVO>> getStudentListByMakeup(@RequestBody List<Long> ids){
        return R.ok(studentService.getStudentListByMakeup(ids));
    }

    /**
     * 查询门店运营数据-学员数据
     * @param studentDTO
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.store.vo.StoreStudentDataStatisticsVO>
     * <AUTHOR>
     * @date 2025/3/10 17:24
     */
    @Operation(summary = "查询门店运营数据-学员数据", description = "查询门店运营数据-学员数据")
    @PostMapping("/getStudentCountByStoreId")
    @Inner
    public R<StoreStudentDataStatisticsVO> getStudentCountByStoreId(@RequestBody StudentDTO studentDTO) {
        return R.ok(studentService.getStudentCountByStoreId(studentDTO));
    }

    /**
     * 查询门店运营数据-课消数据
     * @param studentDTO
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.store.vo.StoreCourseHoursDataStatisticsVO>
     * <AUTHOR>
     * @date 2025/3/11 10:48
     */
    @Operation(summary = "查询门店运营数据-课消数据", description = "查询门店运营数据-课消数据")
    @PostMapping("/getStudentCourseHoursByStoreId")
    @Inner
    public R<StoreCourseHoursDataStatisticsVO> getStudentCourseHoursByStoreId(@RequestBody StudentDTO studentDTO) {
        return R.ok(studentService.getStudentCourseHoursByStoreId(studentDTO));
    }



    /**
     * 查询门店运营数据-续费数据
     * @param studentDTO
     * @return com.yuedu.ydsf.common.core.util.R<com.yuedu.store.vo.StoreRenewDataStatisticsVO>
     * <AUTHOR>
     * @date 2025/3/24 16:39
     */
    @Operation(summary = "查询门店运营数据-续费数据", description = "查询门店运营数据-续费数据")
    @PostMapping("/getStoreRenewDataStatistics")
    @Inner
    public R<StoreRenewDataStatisticsVO> getStoreRenewDataStatistics(@RequestBody StudentDTO studentDTO) {
        return R.ok(studentService.getStoreRenewDataStatistics(studentDTO));
    }


    /**
     *  根据手机号查询老学员信息
     *
     * <AUTHOR>
     * @date 2025年04月08日 09时59分
     */
    @Operation(summary = "根据手机号查询老学员信息", description = "根据手机号查询老学员信息")
    @GetMapping("/getOldStudentInfoByPhone")
    @Inner(value = false)
    public R<List<StudentVO>> getOldStudent(
            @Valid  @NotNull(message = "门店ID不能为空") @Parameter(description = "门店ID")  Long storeId,
            @Valid  @NotBlank(message = "手机号不允许为空") @Parameter(description = "手机号")  String phone){
        return R.ok(studentService.getStudentListByPhone(storeId,phone));
    }


    /**
     *  老学员登录
     *
     * <AUTHOR>
     * @date 2025年04月08日 10时11分
     */
    @Operation(summary = "老学员登录", description = "老学员登录")
    @GetMapping("/oldStudentLogin")
    @Inner(value = false)
    public R<String> oldStudentLogin(
            @Valid @NotNull(message = "门店ID不能为空") @Parameter(description = "门店ID")  Long storeId,
            @Valid @NotNull(message = "用户ID不能为空") @Parameter(description = "用户ID")  Long uId
            ){
        return R.ok(studentService.oldStudentLogin(storeId,uId),"登录成功");
    }



    /**
     *  注册验证码发送
     *
     * <AUTHOR>
     * @date 2025年04月08日 15时21分
     */
    @Operation(summary = "注册验证码", description = "注册验证码")
    @GetMapping("/registerSendCode")
    @Inner(value = false)
    @Idempotent(expireTime = 60, key = "#phone",info = "验证码已发送，请勿频繁操作",delKey = false)
    public R registerSendCode(
            @Valid @NotNull(message = "门店ID不能为空") @Parameter(description = "门店ID")  Long storeId,
            @Valid @NotBlank(message = "手机号不允许为空") @Parameter(description = "手机号")  String phone){
        studentService.registerSendCode( storeId,phone);
        return R.ok("验证码发送成功！");
    }


    /**
     *  注册学生信息并登录
     *
     * <AUTHOR>
     * @date 2025年04月08日 15时59分
     */
    @Operation(summary = "注册学生信息", description = "注册学生信息")
    @PostMapping("/registerStudent")
    @Inner(value = false)
    @Idempotent(expireTime = 3,info = "重复提交，请稍后再试")
    public R<String> registerStudent(@Validated(StudentValidGroup.StudentRegisterValidGroup.class) @RequestBody StudentDTO studentDTO){
        return studentService.registerStudent(studentDTO);
    }


    /**
     * (测评)根据班级id获取学员列表
     *
     * condition手机号/姓名、
     * test_status 测评状态、
     * regular学员类型（意向、试听、正式）
     * @return 测评学员列表
     */
    @Operation(summary = "根据门店id获取测评学员列表", description = "根据班级id获取测评学员列表")
    @GetMapping("/getTestByClassId")
    @StorePermission
    public R<Page<StudentTestVO>> getTestByClassId(@ParameterObject Page page,@ParameterObject StudentTestDTO studentTestDTO) {
        studentTestDTO.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(studentService.getTestByClassId(page,studentTestDTO));
    }

    /**
     * 查询学员测评详情
     *
     * @param studentDetailDTO 学员信息
     * @return R
     */
    @Operation(summary = "查询学员详情", description = "查询学员详情")
    @PostMapping("/testDetail")
    @StorePermission
    public R<StudentTestDetailVO> testDetail(@RequestBody StudentDetailDTO studentDetailDTO) {
        Long storeId = StoreContextHolder.getStoreId();
        Integer stuId = studentDetailDTO.getStuId();
        return R.ok(studentService.testDetail(stuId,storeId));
    }

    /**
     * 更改学员阶段
     */
    @Operation(summary = "更改学员阶段", description = "更改学员阶段")
    @PostMapping("/editStudentStageId")
    @Inner
    public R editStudentStageId(@Validated(StudentValidGroup.Edit.class) @RequestBody StudentUpdateDTO studentUpdateDTO) {
        studentService.editStudentStageId(studentUpdateDTO);
        return R.ok();
    }

    /**
     * 新增意向会员(门店录入)
     *
     * @param intentionStudentDTO 意向会员信息
     * @return R
     */
    @Operation(summary = "新增意向会员(门店录入)", description = "新增意向会员(门店录入)")
    @PostMapping("/saveIntentionStudent")
    @StorePermission
    @SysLog("新增意向会员")
    @Idempotent(expireTime = 3, info = "重复提交，请稍后再试",key = "#intentionStudentDTO.phone")
    public R saveIntentionStudent(@Validated(StudentValidGroup.AddIntentionStudent.class) @RequestBody IntentionStudentDTO intentionStudentDTO) {
        return studentService.saveIntentionStudent(intentionStudentDTO);
    }

    /**
     * 获取分享二维码链接
     *
     * @return R<ShareQrCodeVO>
     */
    @Operation(summary = "获取分享二维码链接", description = "获取分享二维码链接")
    @GetMapping("/getShareQrCodeUrl")
    @StorePermission
    public R<ShareQrCodeVO> getShareQrCodeUrl() {
        return R.ok(studentService.getShareQrCodeUrl());
    }

    /**
     * 家长录入意向会员
     *
     * @param parentIntentionStudentDTO 家长录入意向会员信息
     * @return R
     */
    @Operation(summary = "家长录入意向会员", description = "家长录入意向会员")
    @PostMapping("/saveIntentionStudentByParent")
    @Inner(value = false)
    @SysLog("家长录入意向会员")
    @Idempotent(expireTime = 3, info = "重复提交，请稍后再试",key = "#parentIntentionStudentDTO.phone")
    public R saveIntentionStudentByParent(@Validated(StudentValidGroup.ParentAddIntentionStudent.class) @RequestBody ParentIntentionStudentDTO parentIntentionStudentDTO) {
        return studentService.saveIntentionStudentByParent(parentIntentionStudentDTO);
    }

    /**
     * 获取意向会员列表
     *
     * @param page  分页参数
     * @param query 查询条件（支持手机号、姓名或姓名首字母搜索）
     * @return R<IPage < IntentionStudentListVO>>
     */
    @Operation(summary = "获取意向会员列表", description = "获取意向会员列表，支持手机号、姓名或姓名首字母搜索")
    @GetMapping("/getIntentionStudentList")
    @StorePermission
    public R<IPage<IntentionStudentListVO>> getIntentionStudentList(Page<IntentionStudentListVO> page, IntentionStudentQuery query) {
        return R.ok(studentService.getIntentionStudentList(page, query));
    }

    /**
     * 根据意向学生ID获取意向学员详情
     *
     * @param userId 意向学生ID
     * @return R<IntentionStudentDetailVO>
     */
    @Operation(summary = "根据意向学生ID获取意向学员详情", description = "根据意向学生ID获取意向学员详情")
    @GetMapping("/getIntentionStudentDetail")
    @StorePermission
    public R<IntentionStudentDetailVO> getIntentionStudentDetail(@RequestParam @NotNull(message = "学生ID不能为空") Long userId) {
        return R.ok(studentService.getIntentionStudentDetail(userId));
    }

    /**
     * 修改意向会员信息
     *
     * @param updateIntentionStudentDTO 修改意向会员信息
     * @return R
     */
    @Operation(summary = "修改意向会员信息", description = "修改意向会员信息")
    @PostMapping("/updateIntentionStudent")
    @StorePermission
    @SysLog("修改意向会员信息")
    @Idempotent(expireTime = 3, info = "重复提交，请稍后再试", key = "#updateIntentionStudentDTO.userId")
    public R updateIntentionStudent(@Validated(StudentValidGroup.UpdateIntentionStudent.class) @RequestBody UpdateIntentionStudentDTO updateIntentionStudentDTO) {
        return studentService.updateIntentionStudent(updateIntentionStudentDTO);
    }

    /**
     * 根据学生ID查询学生信息
     */
    @Operation(summary = "根据学生ID查询学生信息", description = "根据学生ID查询学生信息")
    @PostMapping("/inner/getByStudentIdList")
    @Inner
    public R<List<StudentMemberDTO>> getByStudentIdList(@RequestBody List<Long> studentIdList){
        if (studentIdList == null || studentIdList.isEmpty()){
            return R.ok(List.of());
        } else {
            return R.ok(studentService.getStudentMemberList(studentIdList));
        }
    }



}
