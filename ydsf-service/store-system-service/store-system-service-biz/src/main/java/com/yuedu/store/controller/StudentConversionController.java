package com.yuedu.store.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.excel.annotation.ResponseExcel;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.store.entity.StudentConversion;
import com.yuedu.store.service.StudentConversionService;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import com.yuedu.ydsf.common.security.annotation.HasPermission;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学生会员转化统计表 控制类
 *
 * <AUTHOR>
 * @date 2025-06-23 14:53:54
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/studentConversion" )
@Tag(description = "store_student_conversion" , name = "学生会员转化统计表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class StudentConversionController {

    private final  StudentConversionService studentConversionService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param studentConversion 学生会员转化统计表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @HasPermission("admin_studentConversion_view")
    public R getStudentConversionPage(@ParameterObject Page page, @ParameterObject StudentConversion studentConversion) {
        LambdaQueryWrapper<StudentConversion> wrapper = Wrappers.lambdaQuery();
        return R.ok(studentConversionService.page(page, wrapper));
    }


    /**
     * 通过条件查询学生会员转化统计表
     * @param studentConversion 查询条件
     * @return R  对象列表
     */
    @Operation(summary = "通过条件查询" , description = "通过条件查询对象" )
    @GetMapping("/details" )
    @HasPermission("admin_studentConversion_view")
    public R getDetails(@ParameterObject StudentConversion studentConversion) {
        return R.ok(studentConversionService.list(Wrappers.query(studentConversion)));
    }

    /**
     * 新增学生会员转化统计表
     * @param studentConversion 学生会员转化统计表
     * @return R
     */
    @Operation(summary = "新增学生会员转化统计表" , description = "新增学生会员转化统计表" )
    @SysLog("新增学生会员转化统计表" )
    @PostMapping("/add")
    @HasPermission("admin_studentConversion_add")
    public R save(@RequestBody StudentConversion studentConversion) {
        return R.ok(studentConversionService.save(studentConversion));
    }

    /**
     * 修改学生会员转化统计表
     * @param studentConversion 学生会员转化统计表
     * @return R
     */
    @Operation(summary = "修改学生会员转化统计表" , description = "修改学生会员转化统计表" )
    @SysLog("修改学生会员转化统计表" )
    @PutMapping("/edit")
    @HasPermission("admin_studentConversion_edit")
    public R updateById(@RequestBody StudentConversion studentConversion) {
        return R.ok(studentConversionService.updateById(studentConversion));
    }

    /**
     * 通过id删除学生会员转化统计表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除学生会员转化统计表" , description = "通过id删除学生会员转化统计表" )
    @SysLog("通过id删除学生会员转化统计表" )
    @DeleteMapping("/delete")
    @HasPermission("admin_studentConversion_del")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(studentConversionService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param studentConversion 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @HasPermission("admin_studentConversion_export")
    public List<StudentConversion> exportExcel(StudentConversion studentConversion,Long[] ids) {
        return studentConversionService.list(Wrappers.lambdaQuery(studentConversion).in(ArrayUtil.isNotEmpty(ids), StudentConversion::getId, ids));
    }

    /**
     * 导入excel 表
     * @param studentConversionList 对象实体列表
     * @param bindingResult 错误信息列表
     * @return ok fail
     */
    @PostMapping("/import")
    @HasPermission("admin_studentConversion_export")
    public R importExcel(@RequestExcel List<StudentConversion> studentConversionList, BindingResult bindingResult) {
        return R.ok(studentConversionService.saveBatch(studentConversionList));
    }
}
