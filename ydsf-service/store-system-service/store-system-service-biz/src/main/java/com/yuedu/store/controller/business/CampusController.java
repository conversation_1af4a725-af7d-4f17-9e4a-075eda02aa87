package com.yuedu.store.controller.business;

import com.yuedu.store.service.CampusService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;


/**
 * 门店
 *
 * <AUTHOR>
 * @date 2025-4-25 08:32:19
 */
@RestController("businessCampus")
@RequiredArgsConstructor
@RequestMapping("/business/campus")
@Tag(description = "businessCampus", name = "门店信息")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class CampusController {

    private final CampusService campusService;

    /**
     * 获取该校区下的门店
     *
     * @return R
     */
    @Operation(description = "获取该校区下的门店", summary = "获取该校区下的门店")
    @GetMapping("/storeBySchoolId")
    public R storeBySchoolId() {
        return R.ok(campusService.getStoreBySchoolId(StoreContextHolder.getSchoolId()));

    }

}