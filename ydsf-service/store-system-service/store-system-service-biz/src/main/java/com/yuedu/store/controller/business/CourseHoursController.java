package com.yuedu.store.controller.business;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.*;
import com.yuedu.store.query.CourseHoursRefundQuery;
import com.yuedu.store.query.StoreCourseHoursPayQuery;
import com.yuedu.store.query.StoreRefundRecordQuery;
import com.yuedu.store.service.CourseHoursPayService;
import com.yuedu.store.valid.RefundValidGroup;
import com.yuedu.store.vo.CourseHoursPayVO;
import com.yuedu.store.vo.StoreCourseHoursPayVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;


/**
 * 学员课次相关
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/business/courseHours")
@Tag(description = "business/courseHours", name = "学员课次相关")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class CourseHoursController {

    private final CourseHoursPayService courseHoursPayService;

    /**
     * 为学员录入课次
     *
     * @param params 课次信息
     * @return R
     */
    @Operation(summary = "为学员录入课次", description = "为学员录入课次")
    @PostMapping("/save")
    @SysLog("学员课次录入")
    public R saveCourseHour(@RequestBody @Valid CourseHoursPayDTO params) {
        params.setStoreId(StoreContextHolder.getStoreId());
        params.setSchoolId(StoreContextHolder.getSchoolId());
        courseHoursPayService.saveCourseHour(params);
        return R.ok();
    }

    /**
     * 学员退费    @Operation(summary = "学员全部退费", description = "学员全部退费")
     *
     * @param studentRefundDTO 学员退费DTO类
     * @return R
     */
    @Operation(summary = "学员全部退费", description = "学员全部退费")
    @PostMapping("/refund")
    @SysLog("学员全部退费")
    public R refundStudent(@RequestBody @Valid StudentRefundDTO studentRefundDTO) {
        courseHoursPayService.refundStudent(StoreContextHolder.getSchoolId(), StoreContextHolder.getStoreId(), studentRefundDTO.getStudentId(), studentRefundDTO.getRefundDate());
        return R.ok();
    }


    /**
     * 可退费列表
     *
     * @param params 学员退费DTO类
     * @return R
     */
    @Operation(summary = "可退费列表", description = "可退费列表")
    @GetMapping("/refundList")
    public R<List<CourseHoursPayVO>> refundStudent(@ParameterObject @Valid CourseHoursRefundQuery params) {
        params.setSchoolId(StoreContextHolder.getSchoolId());
        return R.ok(courseHoursPayService.refundList(params));
    }


    /**
     * 学员部分退费
     *
     * @param params 学员退费DTO类
     * @return R
     */
    @Operation(summary = "学员部分退费", description = "学员部分退费")
    @PostMapping("/refundPart")
    @SysLog("学员部分退费")
    public R refundPartStudent(@RequestBody @Validated(RefundValidGroup.PartRefund.class) CoursePartRefundDTO params) {
        log.info("学员部分退费的参数:{}", JSON.toJSONString(params));
        params.setSchoolId(StoreContextHolder.getSchoolId());
        params.setStoreId(StoreContextHolder.getStoreId());
        courseHoursPayService.refundAmount(params,false);
        return R.ok();
    }

    /**
     * 学员可退费金额
     *
     * @param params 学员退费DTO类
     * @return R
     */
    @Operation(summary = "学员可退费金额", description = "学员可退费金额")
    @PostMapping("/refundAmount")
    public R<BigDecimal> refundAmount(@RequestBody @Validated(RefundValidGroup.Amount.class) CoursePartRefundDTO params) {
        params.setSchoolId(StoreContextHolder.getSchoolId());
        params.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(courseHoursPayService.refundAmount(params,true));
    }

    /**
     * 作废收费单
     *
     * @param params 作废收费单
     * @return R
     */
    @Operation(summary = "作废收费单", description = "作废收费单")
    @PostMapping("/revoke")
    public R cancel(@RequestBody @Valid CourseHoursRevokeDTO params) {
        params.setSchoolId(StoreContextHolder.getSchoolId());
        params.setStoreId(StoreContextHolder.getStoreId());
        courseHoursPayService.revoke(params);
        return R.ok();
    }

    /**
     * 收费单列表（分页）
     *
     * @param storeCourseHoursPayQuery 收费单列表
     * @return R
     */
    @Operation(summary = "收费单列表", description = "收费单列表")
    @GetMapping("/getBillPage")
    public R<Page<StoreCourseHoursPayVO>> getBillPage(@ParameterObject Page page, @ParameterObject StoreCourseHoursPayQuery storeCourseHoursPayQuery) {
        storeCourseHoursPayQuery.setSchoolId(StoreContextHolder.getSchoolId());
        return R.ok(courseHoursPayService.getBillPage(page, storeCourseHoursPayQuery));
    }

    /**
     * web端收费单列表（分页）
     *
     * @param storeCourseHoursPayQuery 收费单列表
     * @return R
     */
    @Operation(summary = "收费单列表", description = "收费单列表")
    @GetMapping("/getWebBillPage")
    public R<Page<StoreCourseHoursPayVO>> getWebBillPage(@ParameterObject Page page, @ParameterObject StoreCourseHoursPayQuery storeCourseHoursPayQuery) {
        storeCourseHoursPayQuery.setStoreId(StoreContextHolder.getStoreId());
        storeCourseHoursPayQuery.setSchoolId(StoreContextHolder.getSchoolId());
        return R.ok(courseHoursPayService.getBillPage(page, storeCourseHoursPayQuery));
    }

    /**
     * 收费单详情
     *
     * @param id 收费单详情
     * @return R
     */
    @Operation(summary = "收费单详情", description = "收费单详情")
    @GetMapping("/getBillById")
    public R getBillById(@RequestParam Long id) {
        return R.ok(courseHoursPayService.getBillById(id));
    }

    /**
     * 编辑收费单
     *
     * @param params 编辑收费单
     * @return R
     */
    @Operation(summary = "编辑收费单", description = "编辑收费单")
    @PostMapping("/editBil")
    public R edit(@RequestBody @Valid CourseHoursEditDTO params) {
        params.setSchoolId(StoreContextHolder.getSchoolId());
        courseHoursPayService.edit(params);
        return R.ok();
    }

}
