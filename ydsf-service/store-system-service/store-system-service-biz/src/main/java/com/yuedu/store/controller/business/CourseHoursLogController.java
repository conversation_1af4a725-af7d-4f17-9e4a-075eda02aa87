package com.yuedu.store.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.query.CourseHoursLogNewQuery;
import com.yuedu.store.service.StoreCourseHoursLogService;
import com.yuedu.store.vo.CourseHoursLogNewVO;
import com.yuedu.store.vo.CourseHoursLogTotalVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 课时购买日志表
 *
 * <AUTHOR>
 * @date 2025-06-04 14:33
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("business/CourseHoursLog")
@Tag(description = "business/CourseHoursLog", name = "课时购买日志表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission

public class CourseHoursLogController {

    @Resource
    private StoreCourseHoursLogService storeCourseHoursLogService;


    /**
     * 查询课消报表
     */
    @Operation(summary = "查询课消报表", description = "查询课消报表")
    @GetMapping("/getCoursePage")
    public R<Page<CourseHoursLogNewVO>> getCoursePage(@ParameterObject Page page, @ParameterObject CourseHoursLogNewQuery courseHoursLogNewQuery) {
        courseHoursLogNewQuery.setStoreId(StoreContextHolder.getStoreId());
        courseHoursLogNewQuery.setSchoolId(StoreContextHolder.getSchoolId());
        return R.ok(storeCourseHoursLogService.getCourseList(page,courseHoursLogNewQuery));
    }

    /**
     * 课消合计
     */
    @Operation(summary = "课消合计", description = "课消合计")
    @GetMapping("/getCourseAll")
    public R<CourseHoursLogTotalVO> getCourseAll(@ParameterObject CourseHoursLogNewQuery courseHoursLogNewQuery) {
        courseHoursLogNewQuery.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(storeCourseHoursLogService.getCourseAll(courseHoursLogNewQuery));
    }

    /**
     * 导出明细
     *
     * @param courseHoursLogNewQuery 查询条件
     * @return excel 文件流
     */
    @Operation(summary = "导出明细", description = "导出明细")
    @GetMapping("/export")
    public R<List<CourseHoursLogNewVO>> exportExcel(CourseHoursLogNewQuery courseHoursLogNewQuery) {
        courseHoursLogNewQuery.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(storeCourseHoursLogService.getCourseListExport(courseHoursLogNewQuery));
    }

}
