package com.yuedu.store.controller.business;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.query.EmployeeCampusQuery;
import com.yuedu.store.service.EmployeeCampusService;
import com.yuedu.store.vo.EmployeeCampusInfoVO;
import com.yuedu.store.vo.EmployeeVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 员工信息
 *
 * <AUTHOR>
 * @date 2025-4-25 08:32:19
 */
@RestController("businessEmployee")
@RequiredArgsConstructor
@RequestMapping("/web/employee")
@Tag(description = "employee", name = "员工信息")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class EmployeeController {

    private final EmployeeCampusService employeeCampusService;

    /**
     * 根据员工ID查询员工详细信息
     *
     * @param userId     员工ID
     * @return StoreEmployeeVO
     */
    @Operation(description = "通过主键查询员工附属属性表", summary = "通过主键查询员工附属属性表")
    @GetMapping("/getById/{userId}")
    public R<EmployeeCampusInfoVO> getById(@PathVariable Long userId) {
        Long storeId = StoreContextHolder.getStoreId();
        return R.ok(employeeCampusService.getEmployeeCampusDetail(userId, storeId));
    }

    /**
     * 根据校区ID查询老师
     */
    @Operation(summary = "根据校区ID查询老师", description = "根据校区ID查询老师")
    @GetMapping("/getInfoBySchoolId")
    public R<List<EmployeeVO>> getInfoBySchoolId() {
        return R.ok(employeeCampusService.getEmployeeBySchoolId(StoreContextHolder.getSchoolId()));
    }


    /**
     * 根据门店ID查询老师
     */
    @Operation(summary = "获取门店老师", description = "获取门店老师")
    @GetMapping("/getEmployeeByRoles")
    public R<List<EmployeeVO>> getEmployeeByRoles(@RequestParam(required = false, defaultValue = "") String roles) {
        return R.ok(employeeCampusService.getEmployeeByRoles(StoreContextHolder.getStoreId(), roles));
    }


    /**
     * 查询全部员工
     *
     * @return 全部员工信息列表
     */
    @GetMapping("/listByStoreId")
    @Operation(summary = "查询全部员工", description = "查询全部员工")
    public R<IPage<EmployeeCampusInfoVO>> listByStoreId(@ParameterObject Page page, @ParameterObject EmployeeCampusQuery temployeeCampusQuery) {
        temployeeCampusQuery.setStoreId(StoreContextHolder.getStoreId());
        temployeeCampusQuery.setTerminal("STORE");
        return R.ok(employeeCampusService.page(page,temployeeCampusQuery));
    }

    /**
     * 查询全部员工
     *
     * @return 全部员工信息列表
     */
    @GetMapping("/listBySchoolId")
    @Operation(summary = "查询全部员工", description = "查询全部员工")
    public R<IPage<EmployeeCampusInfoVO>> listBySchoolId(@ParameterObject Page page) {
        return R.ok(employeeCampusService.listSchoolEmployeeCampus(page));
    }
}