package com.yuedu.store.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.entity.XgjStudent;
import com.yuedu.store.service.StudentMigrateService;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据迁移
 *
 * <AUTHOR>
 * @date 2025-4-21 13:59:19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/migrate")
@Tag(description = "migrate", name = "数据迁移")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class MigrateController {

    private final StudentMigrateService studentMigrateService;

    /**
     * 分页查询
     *
     * @param page   分页对象
     * @param params 查询参数
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    public R<Page<XgjStudent>> getStoreSchoolPage(@ParameterObject Page page, @ParameterObject XgjStudent params) {
        params.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(studentMigrateService.pageQuery(page, params));
    }

    /**
     * 保存
     *
     * @param params 参数
     */
    @Operation(summary = "保存", description = "保存")
    @PostMapping("/save")
    public R save(@RequestBody List<XgjStudent> params) {
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();
        studentMigrateService.save(params, storeId, schoolId);
        return R.ok();
    }

}