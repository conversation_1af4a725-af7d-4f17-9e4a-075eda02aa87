package com.yuedu.store.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.query.StoreRefundRecordQuery;
import com.yuedu.store.service.RefundRecordService;
import com.yuedu.store.vo.StoreRefundRecordVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;



/**
 * 退费相关
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/business/refundRecord")
@Tag(description = "business/refundRecord", name = "退费相关")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class RefundRecordController {
     private final RefundRecordService refundRecordService;
    /**
     * web端退费列表（分页）
     *
     * @param storeRefundRecordQuery 退费列表
     * @return R
     */
    @Operation(summary = "退费列表", description = "退费列表")
    @GetMapping("/getRefundRecordPage")
    public R<Page<StoreRefundRecordVO>> getRefundRecordPage(@ParameterObject Page page, @ParameterObject StoreRefundRecordQuery storeRefundRecordQuery) {
        storeRefundRecordQuery.setStoreId(StoreContextHolder.getStoreId());
        storeRefundRecordQuery.setSchoolId(StoreContextHolder.getSchoolId());
        return R.ok(refundRecordService.getRefundRecordPage(page, storeRefundRecordQuery));
    }
}
