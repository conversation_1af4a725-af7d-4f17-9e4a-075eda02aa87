package com.yuedu.store.controller.business;

import com.yuedu.store.dto.StoreDTO;
import com.yuedu.store.service.StoreBillService;
import com.yuedu.store.valid.StoreBillValidGroup;
import com.yuedu.store.vo.StoreBillVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 收费单相关
 *
 * <AUTHOR>
 * @date 2025-05-28 11:31
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/business/bill")
@Tag(description = "bill", name = "收费单")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class StoreBillController {

    private final StoreBillService storeBillService;

     /**
     * 上传电子章
     *
     * @param storeDTO 员工附属属性表
     * @return Boolean
     */
    @Operation(description = "上传电子章", summary = "上传电子章")
    @PutMapping("/updateBill")
    public R<Boolean> updateBill(@Validated(StoreBillValidGroup.UpdateBill.class) @RequestBody StoreDTO storeDTO) {
        storeDTO.setId(StoreContextHolder.getStoreId());
        return R.ok(storeBillService.updateBill(storeDTO));
    }

    /**
     * 查询电子章
     * @param
     */
    @GetMapping("/getById")
    public R<StoreBillVO> getById() {
        Long storeId = StoreContextHolder.getStoreId();
        return R.ok(storeBillService.getBillDetail(storeId));
    }

}