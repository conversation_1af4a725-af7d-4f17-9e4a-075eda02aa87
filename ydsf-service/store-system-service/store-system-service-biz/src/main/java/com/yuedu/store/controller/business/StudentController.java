package com.yuedu.store.controller.business;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.StudentChangeStoreDTO;
import com.yuedu.store.dto.StudentUpdateDTO;
import com.yuedu.store.query.StudentQuery;
import com.yuedu.store.service.StudentService;
import com.yuedu.store.valid.StudentValidGroup;
import com.yuedu.store.vo.*;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.excel.annotation.RequestExcel;
import com.yuedu.ydsf.common.log.annotation.SysLog;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 学员管理
 *
 * <AUTHOR>
 * @date 2025-4-25 08:32:19
 */
@RestController("businessStudent")
@RequiredArgsConstructor
@RequestMapping("/business/student")
@Tag(description = "business/student", name = "学员管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class StudentController {

    private final StudentService studentService;


    /**
     * 新增学员
     *
     * @return R
     */
    @Operation(summary = "新增学员", description = "新增学员")
    @PostMapping("/saveStudent")
    @SysLog("新增学员")
    public R<SaveStudentErrorVO> saveStudent(@Validated(StudentValidGroup.Add.class) @RequestBody StudentUpdateDTO params) {
        return R.ok(studentService.saveStudent(params));
    }

    /**
     * 编辑学员信息
     *
     * @param studentUpdateDTO 学员
     * @return R
     */
    @Operation(summary = "编辑学员信息", description = "编辑学员信息")
    @PostMapping("/editStudent")
    @SysLog("编辑学员信息")
    public R editStudent(@Validated(StudentValidGroup.Edit.class) @RequestBody StudentUpdateDTO studentUpdateDTO) {
        studentService.editStudent(studentUpdateDTO);
        return R.ok();
    }

    /**
     * 查询学员详情
     *
     * @param studentQuery 学员
     * @return R
     */
    @Operation(summary = "查询学员详情", description = "查询学员详情")
    @GetMapping("/detail")
    public R<StudentVO> detail(@ParameterObject @Valid StudentQuery studentQuery) {
        studentQuery.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(studentService.detail(studentQuery));
    }

    /**
     * 查询学员报表（不区分课程类型）
     */
    @Operation(summary = "查询学员报表", description = "查询学员报表")
    @GetMapping("/getStudentPage")
    public R<Page<StudentFormVO>> getStudentPage(@ParameterObject Page page, @ParameterObject StudentQuery studentQuery) {
        studentQuery.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(studentService.getStudentPage(page,studentQuery));
    }

    /**
     * 查询学员列表
     */
    @Operation(summary = "查询学员报表列表", description = "查询学员报表列表")
    @GetMapping("/getStudentList")
    public R<List<StudentFormVO>> getStudentList(@ParameterObject StudentQuery studentQuery) {
        studentQuery.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(studentService.getStudentList(studentQuery));
    }
    /**
     * 查询学员剩余课时列表
     */
    @Operation(summary = "查询学员剩余课时列表", description = "根据学员id获取剩余课时列表")
    @GetMapping("/getHoursList")
    public R<List<StudentHoursVO>> getHoursList(@RequestParam(name = "stuId") Integer stuId) {
        Long storeId = StoreContextHolder.getStoreId();
        return R.ok(studentService.getHoursList(storeId,stuId));
    }

    /**
     * 转门店
     *
     * @param params 参数
     * @return R
     */
    @Operation(description = "转门店", summary = "转门店")
    @PostMapping("/changeStore")
    public R changeStore(@RequestBody @Valid StudentChangeStoreDTO params) {
        Long schoolId =  StoreContextHolder.getSchoolId();
        Long storeId = StoreContextHolder.getStoreId();
        studentService.changeStore(schoolId, storeId, params.getNewStoreId(), params.getUserId());
        return R.ok();
    }

    /**
     * 导入学员列表
     *
     * @param file          文件
     * @param bindingResult 绑定结果
     * @return R
     */
    @Operation(summary = "导入学员列表", description = "导入学员列表")
    @PostMapping("/import")
    public R<ImportStudentVO> importStudent(@RequestExcel(headRowNumber = 1, ignoreEmptyRow = true) List<BatchImportStudentVO> file,
                                            BindingResult bindingResult) {
        return studentService.importStudent(file, bindingResult);
    }
}
