package com.yuedu.store.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.store.dto.StudentTestDTO;
import com.yuedu.store.service.StudentService;
import com.yuedu.store.vo.*;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.StorePermission;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

/**
 * 测评控制器
 *
 * <AUTHOR>
 * @date 2025-4-25 13:51
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/web/test")
@Tag(description = "test", name = "测评")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@StorePermission
public class TestPaperController {
    @Resource
    private StudentService studentService;
    /**
     * (测评)根据门店id获取学员列表
     *
     * condition手机号/姓名、
     * test_status 测评状态、
     * regular学员类型（意向、试听、正式）
     * @return 测评学员列表
     */
    @Operation(summary = "根据门店id获取测评学员列表", description = "根据门店id获取测评学员列表")
    @GetMapping("/getTestByStoreId")
    public R<Page<StudentTestVO>> getTestByStoreId(@ParameterObject Page page, @ParameterObject StudentTestDTO studentTestDTO) {
        studentTestDTO.setStoreId(StoreContextHolder.getStoreId());
        return R.ok(studentService.getTestByClassId(page,studentTestDTO));
    }
    /**
     * (测评)根据学员id获取报告列表
     * @return 学员报告列表
     */
    @Operation(summary = "根据学员id获取报告列表", description = "根据学员id获取报告列表")
    @GetMapping("/getChildList")
    public R<StudentChildDetailVO> getChildList(@RequestParam(name = "stuId") Integer stuId) {
        Long storeId = StoreContextHolder.getStoreId();
        return R.ok(studentService.getChildList(storeId,stuId));
    }

    /**
     * (测评)根据学员id获取家长报告列表
     * @return 家长报告列表
     */
    @Operation(summary = "根据学员id获取家长报告列表", description = "根据学员id获取家长报告列表")
    @GetMapping("/getParentList")
    public R<StudentParentDetailVO> getParentList(@RequestParam(name = "stuId") Integer stuId) {
        Long storeId = StoreContextHolder.getStoreId();
        return R.ok(studentService.getParentList(storeId,stuId));
    }
}