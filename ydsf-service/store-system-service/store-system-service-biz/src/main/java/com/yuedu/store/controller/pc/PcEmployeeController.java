package com.yuedu.store.controller.pc;

import com.yuedu.store.service.EmployeeAttrService;
import com.yuedu.store.vo.StoreEmployeeAttrVO;
import com.yuedu.ydsf.common.core.util.R;
import com.yuedu.ydsf.common.security.annotation.Inner;
import com.yuedu.ydsf.common.security.annotation.PcPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 老师 控制类
 *
 * <AUTHOR>
 * @date 2024-11-26 15:09:08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/pcEmployee")
@Tag(description = "t_employee", name = "老师管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Inner(value = false)
public class PcEmployeeController {

    private final EmployeeAttrService storeEmployeeAttrService;

    /**
     * 通过主键查询员工附属属性表
     *
     * @param id 主键
     * @return StoreEmployeeVO
     */
    @Operation(description = "通过主键查询员工附属属性表", summary = "通过主键查询员工附属属性表")
    @GetMapping("/getById/{id}")
    @PcPermission
    public R<StoreEmployeeAttrVO> getById(@PathVariable Long id) {
        return R.ok(storeEmployeeAttrService.getAttrById(id));
    }

}
