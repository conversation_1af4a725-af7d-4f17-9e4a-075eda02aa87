<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.store.mapper.CourseHoursLogMapper">
        <select id="getCourseAll" resultType="map">
                SELECT schl.course_hours, schl.total_amount
                FROM (
                     SELECT
                     schl.*,
                     CAST(t.class_date AS DATE) AS t_class_date,
                     CAST(mup.class_date AS DATE) AS mup_class_date
                     FROM store_course_hours_log schl
                     LEFT JOIN b_timetable t ON t.id = schl.timetable_id
                     LEFT JOIN b_course_make_up_online mup ON mup.id = schl.timetable_id
                     ) schl
                WHERE 1=1
                        <if test="storeId != null">
            AND schl.store_id = #{storeId}
                    </if>
                    <if test="logType != null">
                  AND schl.log_type = #{logType}
                    </if>
                  AND schl.nullify = #{nullify}
                    <if test="startDate != '' and startDate != null and endDate != '' and endDate != null">
                  AND (
                    (CHAR_LENGTH(CAST(schl.timetable_id AS CHAR)) &lt; 10 AND schl.t_class_date BETWEEN #{startDate} AND DATE_ADD(#{endDate}, INTERVAL 23 HOUR))
                   OR
                    (CHAR_LENGTH(CAST(schl.timetable_id AS CHAR)) &gt; 10 AND schl.mup_class_date BETWEEN #{startDate} AND DATE_ADD(#{endDate}, INTERVAL 23 HOUR))
                        )
                    </if>
        </select>

    <select id="getCoursePage" resultType="com.yuedu.store.entity.StoreCourseHoursLog">
        SELECT schl.*
        FROM (
        SELECT
        schl.*,
        CAST(t.class_date AS DATE) AS t_class_date,
        CAST(mup.class_date AS DATE) AS mup_class_date
        FROM store_course_hours_log schl
        LEFT JOIN b_timetable t ON t.id = schl.timetable_id
        LEFT JOIN b_course_make_up_online mup ON mup.id = schl.timetable_id
        ) schl
        WHERE 1=1
        <if test="query.storeId != null">
            AND schl.store_id = #{query.storeId}
        </if>
        <if test="query.logType != null">
            AND schl.log_type = #{query.logType}
        </if>
        AND schl.nullify = #{query.nullify}
        <if test="query.startDate != '' and query.startDate != null and query.endDate != '' and query.endDate != null">
            AND (
            (CHAR_LENGTH(CAST(schl.timetable_id AS CHAR)) &lt; 10 AND schl.t_class_date BETWEEN #{query.startDate} AND DATE_ADD(#{query.endDate}, INTERVAL 23 HOUR))
            OR
            (CHAR_LENGTH(CAST(schl.timetable_id AS CHAR)) &gt; 10 AND schl.mup_class_date BETWEEN #{query.startDate} AND DATE_ADD(#{query.endDate}, INTERVAL 23 HOUR))
            )
        </if>
        ORDER BY schl.create_time DESC
    </select>

    <select id="getCourseList" resultType="com.yuedu.store.entity.StoreCourseHoursLog">
        SELECT schl.*
        FROM (
        SELECT
        schl.*,
        CAST(t.class_date AS DATE) AS t_class_date,
        CAST(mup.class_date AS DATE) AS mup_class_date
        FROM store_course_hours_log schl
        LEFT JOIN b_timetable t ON t.id = schl.timetable_id
        LEFT JOIN b_course_make_up_online mup ON mup.id = schl.timetable_id
        ) schl
        WHERE 1=1
        <if test="storeId != null">
            AND schl.store_id = #{storeId}
        </if>
        <if test="logType != null">
            AND schl.log_type = #{logType}
        </if>
        AND schl.nullify = #{nullify}
        <if test="startDate != '' and startDate != null and endDate != '' and endDate != null">
            AND (
            (CHAR_LENGTH(CAST(schl.timetable_id AS CHAR)) &lt; 10 AND schl.t_class_date BETWEEN #{startDate} AND DATE_ADD(#{endDate}, INTERVAL 23 HOUR))
            OR
            (CHAR_LENGTH(CAST(schl.timetable_id AS CHAR)) &gt; 10 AND schl.mup_class_date BETWEEN #{startDate} AND DATE_ADD(#{endDate}, INTERVAL 23 HOUR))
            )
        </if>
        ORDER BY schl.create_time DESC
    </select>

</mapper>
