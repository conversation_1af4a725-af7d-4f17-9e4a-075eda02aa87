<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~
  ~      Copyright (c) 2018-2025, ydsf All rights reserved.
  ~
  ~  Redistribution and use in source and binary forms, with or without
  ~  modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~  this list of conditions and the following disclaimer.
  ~  Redistributions in binary form must reproduce the above copyright
  ~  notice, this list of conditions and the following disclaimer in the
  ~  documentation and/or other materials provided with the distribution.
  ~  Neither the name of the pig4cloud.com developer nor the names of its
  ~  contributors may be used to endorse or promote products derived from
  ~  this software without specific prior written permission.
  ~  Author: ydsf
  ~
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.store.mapper.MenuMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yuedu.store.entity.Menu">
        <id column="menu_id" property="menuId"/>
        <result column="name" property="name"/>
        <result column="permission" property="permission"/>
        <result column="path" property="path"/>
        <result column="component" property="component"/>
        <result column="parent_id" property="parentId"/>
        <result column="icon" property="icon"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="menu_type" property="menuType"/>
        <result column="keep_alive" property="keepAlive"/>
        <result column="visible" property="visible"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="embedded" property="embedded"/>
        <result column="visible" property="visible"/>
    </resultMap>

    <!--通过角色查询菜单信息-->
    <select id="listMenusByRoleId" resultMap="BaseResultMap">
        SELECT store_menu.menu_id,
               store_menu.name,
               store_menu.permission,
               store_menu.path,
               store_menu.component,
               store_menu.parent_id,
               store_menu.icon,
               store_menu.sort_order,
               store_menu.keep_alive,
               store_menu.menu_type,
               store_menu.create_time,
               store_menu.update_time,
               store_menu.del_flag,
               store_menu.embedded,
               store_menu.visible
        FROM store_menu
                 LEFT JOIN store_role_menu ON store_menu.menu_id = store_role_menu.menu_id
        WHERE store_menu.del_flag = '0'
          AND store_role_menu.role_id = #{roleId}
        ORDER BY store_menu.sort_order DESC
    </select>

    <!--通过角色ID 查询权限-->
    <select id="listPermissionsByRoleIds" resultType="java.lang.String">
        SELECT m.permission
        FROM store_menu m,
             store_role_menu rm
        WHERE m.menu_id = rm.menu_id
          AND m.del_flag = '0'
          AND rm.role_id IN (#{roleIds})
    </select>
</mapper>
