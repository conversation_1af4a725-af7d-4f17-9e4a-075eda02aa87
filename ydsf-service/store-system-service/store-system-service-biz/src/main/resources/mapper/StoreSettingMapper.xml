<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.store.mapper.StoreSettingMapper">

  <resultMap id="storeSettingMap" type="com.yuedu.store.entity.StoreSetting">
        <id property="id" column="id"/>
        <result property="storeId" column="store_id"/>
        <result property="settingKey" column="setting_key"/>
        <result property="settingValue" column="setting_value"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
