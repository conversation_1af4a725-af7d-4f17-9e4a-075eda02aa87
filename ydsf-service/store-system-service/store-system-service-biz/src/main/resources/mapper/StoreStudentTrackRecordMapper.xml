<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.store.mapper.StoreStudentTrackRecordMapper">

    <resultMap id="BaseResultMap" type="com.yuedu.store.entity.StoreStudentTrackRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="schoolId" column="school_id" jdbcType="INTEGER"/>
            <result property="storeId" column="store_id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="willingnessLevel" column="willingness_level" jdbcType="INTEGER"/>
            <result property="communicationRecords" column="communication_records" jdbcType="VARCHAR"/>
            <result property="recommendTeacher" column="recommend_teacher" jdbcType="INTEGER"/>
            <result property="recommendStudent" column="recommend_student" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 获取学员的跟踪记录（排除初始化记录） -->
    <select id="getTrackRecordsByUserIds" resultMap="BaseResultMap">
        SELECT * FROM store_student_track_record
        WHERE del_flag = 0
        AND store_id = #{storeId}
        AND user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND id NOT IN (
            SELECT MIN(id) FROM store_student_track_record
            WHERE del_flag = 0
            AND store_id = #{storeId}
            AND user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            GROUP BY user_id
        )
        ORDER BY user_id, create_time DESC
    </select>

</mapper>
