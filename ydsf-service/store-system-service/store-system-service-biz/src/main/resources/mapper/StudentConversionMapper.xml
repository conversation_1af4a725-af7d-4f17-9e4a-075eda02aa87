<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuedu.store.mapper.StudentConversionMapper">

  <resultMap id="studentConversionMap" type="com.yuedu.store.entity.StudentConversion">
        <id property="id" column="id"/>
        <result property="studentId" column="student_id"/>
        <result property="storeId" column="store_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="intentionTime" column="intention_time"/>
        <result property="trialTime" column="trial_time"/>
        <result property="formalTime" column="formal_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>
