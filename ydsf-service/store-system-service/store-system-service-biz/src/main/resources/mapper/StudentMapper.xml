<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuedu.store.mapper.StudentMapper">

        <select id="getTestStudentList" resultType="com.yuedu.store.vo.StudentTestVO">
                SELECT
                stu.user_id,
                stu.store_id,
                stu.school_id,
                stu.username,
                sch_student_id,
                stu.NAME,
                stu.phone,
                stu.sex,
                stu.fulltime_school,
                stu.grade,
                stu.grade_base_date,
                stu.STATUS,
                stu.init_regular,
                stu.source,
                stu.is_regular_students,
                stu.stage_id,
                stu.birthday,
                pu.test_number,
                pu.test_score,
                pu.test_time,
                pu.parent_test_number,
                pu.is_report
                FROM store_student as stu
                LEFT JOIN paper_user as pu ON stu.user_id = pu.user_id
                WHERE stu.del_flag = 0
                AND stu.store_id = #{storeId}
                <if test="regular != null and regular != 100">
                        AND stu.is_regular_students = #{regular}
                </if>
                <if test="condition != null and condition != 'undefined'">
                        AND (stu.NAME LIKE CONCAT('%', #{condition}, '%') OR stu.phone LIKE CONCAT('%', #{condition}, '%') OR stu.pinyin_pre LIKE CONCAT('%', #{condition}, '%'))
                </if>
                <if test="testStatus == 1">
                        AND pu.test_number > 0
                </if>
                <if test="testStatus == 0">
                        AND COALESCE(pu.test_number, 0) = 0
                </if>
                ORDER BY pu.test_time DESC, stu.user_id DESC
        </select>

        <select id="getStudentHoursList" resultType="com.yuedu.store.vo.StudentFormVO">
                SELECT
                s.*,
                schs.course_type AS studentCourseType,
                schs.course_hours AS studentCourseHours,
                schs.formal AS studentFormal,
                schs.gift AS studentGift
                FROM store_student s
                LEFT JOIN store_course_hours_student schs ON s.user_id = schs.student_id
                WHERE 1=1
                <if test="storeId != null">
                        AND s.store_id = #{storeId}
                </if>
                <if test="name != null and name != ''">
                        AND (s.name LIKE CONCAT('%', #{name}, '%') OR s.pinyin_pre LIKE CONCAT('%', #{name}, '%'))
                </if>
                <if test="phone != null and phone != ''">
                        AND s.phone = #{phone}
                </if>
                <if test="isRegularStudents != null and isRegularStudents != ''">
                        AND s.is_regular_students = #{isRegularStudents}
                </if>
                <if test="status != null and status != ''">
                        AND s.status IN
                        <foreach collection="statusList" item="item" open="(" separator="," close=")">
                                #{item}
                        </foreach>
                </if>
                <if test="stageId != null and stageId != ''">
                        AND s.stage_id = #{stageId}
                </if>
                <if test="responsiblePerson != null and responsiblePerson != ''">
                        AND s.responsible_person = #{responsiblePerson}
                </if>
                <if test="courseHours != null and courseHours != ''">
                        AND s.course_hours &lt;= #{courseHours}
                </if>
                <if test="startDate != '' and endDate != '' and startDate != null and endDate != null ">
                        AND s.create_time BETWEEN #{startDate} AND DATE_ADD(#{endDate}, INTERVAL 23 HOUR)
                </if>
                ORDER BY s.create_time DESC, s.user_id DESC
        </select>

        <!-- 获取意向会员列表 -->
        <select id="getIntentionStudentList" resultType="com.yuedu.store.vo.IntentionStudentListVO">
                SELECT
                stu.user_id,
                stu.name,
                stu.sex,
                stu.phone,
                stu.create_time
                FROM store_student stu
                WHERE stu.del_flag = 0
                AND stu.store_id = #{storeId}
                AND stu.is_regular_students = 2
                <if test="condition != null and condition != '' and condition != 'undefined'">
                    AND (stu.name LIKE CONCAT('%', #{condition}, '%') OR stu.phone LIKE CONCAT('%', #{condition}, '%') OR stu.pinyin_pre LIKE CONCAT('%', #{condition}, '%'))
                </if>
                ORDER BY stu.create_time DESC
        </select>

        <!-- 获取门店意向会员总数 -->
        <select id="getIntentionStudentCount" resultType="java.lang.Long">
                SELECT COUNT(1)
                FROM store_student
                WHERE del_flag = 0
                AND store_id = #{storeId}
                AND is_regular_students = 2
        </select>

</mapper>
