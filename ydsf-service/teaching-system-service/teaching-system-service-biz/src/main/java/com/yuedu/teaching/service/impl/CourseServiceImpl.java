package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuedu.store.api.feign.RemoteCampusService;
import com.yuedu.store.constant.enums.SchoolCampusEnum;
import com.yuedu.store.dto.CampusDTO;
import com.yuedu.store.vo.CampusVO;
import com.yuedu.teaching.constant.AuthStatusEnum;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.constant.enums.ChargeMethodEnum;
import com.yuedu.teaching.constant.enums.DatabaseOperationTypeEnum;
import com.yuedu.teaching.constant.enums.FeeOptTypeEnum;
import com.yuedu.teaching.constant.enums.TeachingTypeEnum;
import com.yuedu.teaching.dto.*;
import com.yuedu.teaching.entity.*;
import com.yuedu.teaching.manager.CourseFeeManager;
import com.yuedu.teaching.manager.CoursePubManager;
import com.yuedu.teaching.mapper.*;
import com.yuedu.teaching.service.*;
import com.yuedu.teaching.utils.TraceLogUtils;
import com.yuedu.teaching.vo.CampusAuthVO;
import com.yuedu.teaching.vo.CourseVO;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.core.exception.CheckedException;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import com.yuedu.ydsf.eduConnect.api.feign.RemoteEduTimetableService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yuedu.teaching.constant.TeachingConstant.COURSE_ID;

/**
 * 课程表 服务类
 *
 * <AUTHOR>
 * @date 2024-10-24 08:53:59
 */
@Slf4j
@Service
@AllArgsConstructor
@RefreshScope
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements CourseService {
    private RedisTemplate<String, String> redisTemplate;

    private LessonService lessonService;

    private TraceLogUtils traceLogUtils;

    private CourseMapper courseMapper;

    private LessonPubService lessonPubService;

    private CoursePubService coursePubService;

    private LessonMapper lessonMapper;

    private CourseVersionMapper courseVersionMapper;

    private CoursePubMapper coursePubMapper;

    private final CoursePubManager coursePubManager;

    private final CourseAuthStoreHisService courseAuthStoreHisService;

    private final CourseAuthStoreService courseAuthStoreService;

    private final RemoteCampusService remoteCampusService;

    private final CourseTypeService courseTypeService;

    private final CourseFeeManager courseFeeManager;

    @Value("${sync.courseFee:false}")
    private boolean syncCourseFee;

    private final CourseAuthStoreMapper courseAuthStoreMapper;

    private final RemoteEduTimetableService remoteEduTimetableService;



    /**
     * 新增课程表
     *
     * @param courseAddDTO 新增课程表DTO
     * @return Boolean
     */
    @Override
    public Boolean saveCourse(CourseAddDTO courseAddDTO) {
        Course course = BeanUtil.copyProperties(courseAddDTO, Course.class);
        // 课程编号的生成规则：课程类型+年份+月份+序列号
        DateTime date = DateUtil.date();

        course.setCourseCode(TeachingConstant.COURSE_TYPE +
                String.format("%02d", DateUtil.year(date)) +
                String.format("%02d", DateUtil.month(date) + 1) +
                String.format("%03d", getSerialNumber()));
        // 课节数量，默认0
        course.setLessonCount(0);
        // 发布状态，默认未发布
        course.setPublishStatus(TeachingConstant.COURSE_PUBLISH_STATUS_UNPUBLISH);

        // 设置新增字段
        course.setCourseTypeId(courseAddDTO.getCourseTypeId());
        course.setChargeMethod(courseAddDTO.getChargeMethod());
        course.setCustomizeFee(courseAddDTO.getCustomizeFee());

        try {
            this.save(course);

            // 如果有授权门店,保存授权信息
            if (CollUtil.isNotEmpty(courseAddDTO.getAuthStoreIds())) {
                saveAuthStores(course.getId(), courseAddDTO.getAuthStoreIds());

                // 处理新增门店的课程费用
                handleCourseFeeForNewAuthStores(course.getId(), courseAddDTO.getAuthStoreIds(), course);
            }
        } catch (Exception e) {
            log.error("课程保存失败", e);
            throw new BizException("保存失败");
        }

        traceLogUtils.saveTraceLog(course, null, TeachingTypeEnum.COURSE.getCode(), DatabaseOperationTypeEnum.INSERT.getCode());
        return Boolean.TRUE;
    }

    /**
     * 保存授权门店
     *
     * @param id
     * @param authStoreIds
     * @return void
     * <AUTHOR>
     * @date 2025/5/21 13:55
     */
    private void saveAuthStores(Integer id, List<Long> authStoreIds) {
        // 批量保存授权门店
        List<CourseAuthStore> authStores =
                authStoreIds.stream()
                        .map(
                                storeId -> {
                                    CourseAuthStore authStore = new CourseAuthStore();
                                    authStore.setCourseId(id.longValue());
                                    authStore.setStoreId(storeId);
                                    return authStore;
                                })
                        .toList();

        // 批量保存授权历史
        List<CourseAuthStoreHis> authStoreHis =
                authStoreIds.stream()
                        .map(
                                storeId -> {
                                    CourseAuthStoreHis storeHis = new CourseAuthStoreHis();
                                    storeHis.setCourseId(id.longValue());
                                    storeHis.setStoreId(storeId);
                                    storeHis.setAuthStatus(AuthStatusEnum.AUTH_STATUS_0.code); // 正常授权
                                    return storeHis;
                                })
                        .toList();

        // 批量保存
        courseAuthStoreService.saveBatch(authStores);
        courseAuthStoreHisService.saveBatch(authStoreHis);
    }

    /**
     * 修改课程表
     *
     * @param courseUpdateDTO 更新课程表DTO
     * @return Boolean
     */
    @Override
    public Boolean updateCourse(CourseUpdateDTO courseUpdateDTO) {
        Course course = BeanUtil.copyProperties(courseUpdateDTO, Course.class);
        Course oldCourse = this.getById(course.getId());
        if (Objects.isNull(oldCourse)) {
            throw new BizException("当前课程不存在！");
        }
        boolean isPublish = Objects.nonNull(oldCourse.getVersion());

        // 检查不允许修改的字段
        if (isPublish && !ObjectUtil.equal(oldCourse.getCourseTypeId(), course.getCourseTypeId())) {
            throw new BizException("已发布过的课程,不允许修改课程类型");
        }

        if (isPublish && !ObjectUtil.equal(oldCourse.getChargeMethod(), course.getChargeMethod())) {
            throw new BizException("已发布过的课程,不允许修改收费方式");
        }

        if (isPublish && !(oldCourse.getStageId().equals(course.getStageId()))) {
            throw new BizException("已发布过的课程,不能修改课程阶段");
        }

        // 校验课时费标准修改 - 直接比较DTO和原始数据避免类型转换问题
        if (isPublish && !isSameClassHourFee(oldCourse, courseUpdateDTO)) {
            throw new BizException("已发布过的课程,不允许修改课时费标准");
        }

        List<Long> newStoreIds = new ArrayList<>();
        // 处理授权门店变更
        if(Objects.nonNull(courseUpdateDTO.getAuthStoreIds())){
            newStoreIds = handleAuthStoresUpdate(oldCourse.getId(), courseUpdateDTO.getAuthStoreIds());
        }
        // 处理新增门店的课程费用
        if (CollUtil.isNotEmpty(newStoreIds) && isPublish) {
            handleCourseFeeForNewAuthStores(oldCourse.getId(), newStoreIds, course);
        }

        this.updateById(course);
        traceLogUtils.saveTraceLog(course, oldCourse, TeachingTypeEnum.COURSE.getCode(), DatabaseOperationTypeEnum.UPDATE.getCode());
        return Boolean.TRUE;
    }


    /**
     * 比较课时费标准是否相同，处理类型转换问题
     *
     * @param oldCourse       原课程数据
     * @param courseUpdateDTO 更新DTO
     * @return true-相同, false-不同
     */
    private boolean isSameClassHourFee(Course oldCourse, CourseUpdateDTO courseUpdateDTO) {
        // 获取原始值
        BigDecimal oldFee = oldCourse.getCustomizeFee(); // 请根据实际字段名调整

        // 获取DTO中的值，直接处理String到BigDecimal的转换
        BigDecimal newFee = null;
        String feeStr = courseUpdateDTO.getCustomizeFee(); // 统一使用一个字段名

        if (feeStr != null) {
            try {
                newFee = new BigDecimal(feeStr.trim());
            } catch (NumberFormatException e) {
                log.warn("课时费格式转换失败: {}", feeStr);
                return false;
            }
        }

        // 处理null值比较
        if (oldFee == null && newFee == null) {
            return true;
        }
        if (oldFee == null || newFee == null) {
            return false;
        }

        // 比较数值，使用compareTo避免精度问题
        return oldFee.compareTo(newFee) == 0;
    }

    /**
     * 处理新增门店授权的课程费用
     *
     * @param courseId    课程ID
     * @param newStoreIds 新增的门店ID列表
     * @param course      课程信息
     */
    private void handleCourseFeeForNewAuthStores(Integer courseId, List<Long> newStoreIds, Course course) {
        // 检查收费方式是否为自定义
        if (!ChargeMethodEnum.TYPE_1.getCode().equals(course.getChargeMethod())) {
            return;
        }

        // 检查是否有自定义费用
        if (course.getCustomizeFee() == null) {
            log.warn("课程{}收费方式为自定义但未设置自定义费用", courseId);
            return;
        }

        // 为每个新增门店调用费用管理器
        for (Long storeId : newStoreIds) {
            try {
                CourseFeeDTO courseFeeDTO = new CourseFeeDTO();
                courseFeeDTO.setCourseId(courseId.longValue());
                courseFeeDTO.setStoreId(storeId);
                courseFeeDTO.setEffectiveDate(DateUtil.date().toLocalDateTime().toLocalDate());
                courseFeeDTO.setStandardPrice(course.getCustomizeFee());
                courseFeeDTO.setOptType(FeeOptTypeEnum.OPT_TYPE_1.code);

                courseFeeManager.updateCourseFeeByStoreId(courseFeeDTO);
                log.info("为课程{}的门店{}设置自定义费用成功", courseId, storeId);
            } catch (Exception e) {
                log.error("为课程{}的门店{}设置自定义费用失败", courseId, storeId, e);
                // 不抛出异常，避免影响主流程
            }
        }
    }

    /**
     * 处理每次的授权门店的更新
     *
     * @param courseId
     * @param newAuthStoreIds
     * @return 新增的门店ID列表
     * <AUTHOR>
     * @date 2025/5/21 14:45
     */
    private List<Long> handleAuthStoresUpdate(Integer courseId, List<Long> newAuthStoreIds) {
        // 获取当前已授权的门店列表
        List<CourseAuthStore> currentAuthStores =
                courseAuthStoreService.list(
                        Wrappers.lambdaQuery(CourseAuthStore.class).eq(CourseAuthStore::getCourseId, courseId));
        List<Long> currentStoreIds =
                currentAuthStores.stream().map(CourseAuthStore::getStoreId).toList();

        // 找出需要取消授权的门店
        List<Long> cancelStoreIds =
                currentStoreIds.stream().filter(id -> !newAuthStoreIds.contains(id)).toList();

        // 找出需要新增授权的门店
        List<Long> newStoreIds =
                newAuthStoreIds.stream().filter(id -> !currentStoreIds.contains(id)).toList();

        // 处理取消授权的门店
        if (CollUtil.isNotEmpty(cancelStoreIds)) {
            // 删除原授权记录
            courseAuthStoreService.remove(
                    Wrappers.lambdaQuery(CourseAuthStore.class)
                            .eq(CourseAuthStore::getCourseId, courseId)
                            .in(CourseAuthStore::getStoreId, cancelStoreIds));
        }

        // 处理新增授权的门店
        if (CollUtil.isNotEmpty(newStoreIds)) {
            // 保存新授权记录
            List<CourseAuthStore> newAuthStores =
                    newStoreIds.stream()
                            .map(
                                    storeId -> {
                                        CourseAuthStore authStore = new CourseAuthStore();
                                        authStore.setCourseId(courseId.longValue());
                                        authStore.setStoreId(storeId);
                                        return authStore;
                                    })
                            .toList();
            courseAuthStoreService.saveBatch(newAuthStores);
        }

        // 记录完整的授权状态历史 - 每次操作记录所有相关门店的当前状态
        Set<Long> allRelatedStoreIds = new HashSet<>(currentStoreIds); // 当前已授权的门店
        if (CollUtil.isNotEmpty(newAuthStoreIds)) {
            allRelatedStoreIds.addAll(newAuthStoreIds); // 新授权列表中的门店
        }

        // 为所有相关门店创建历史记录
        List<CourseAuthStoreHis> allAuthHis =
                allRelatedStoreIds.stream()
                        .map(
                                storeId -> {
                                    CourseAuthStoreHis storeHis = new CourseAuthStoreHis();
                                    storeHis.setCourseId(courseId.longValue());
                                    storeHis.setStoreId(storeId);
                                    // 根据是否在新授权列表中确定状态
                                    if (CollUtil.isNotEmpty(newAuthStoreIds) && newAuthStoreIds.contains(storeId)) {
                                        storeHis.setAuthStatus(AuthStatusEnum.AUTH_STATUS_0.code); // 正常授权
                                    } else {
                                        storeHis.setAuthStatus(AuthStatusEnum.AUTH_STATUS_1.code); // 取消授权
                                    }
                                    return storeHis;
                                })
                        .toList();

        // 批量保存所有门店的授权状态历史
        if (CollUtil.isNotEmpty(allAuthHis)) {
            courseAuthStoreHisService.saveBatch(allAuthHis);
        }

        // 返回新增的门店ID列表
        return newStoreIds;
    }

    /**
     * 删除课程表
     *
     * @param courseRemoveDTO 删除课程表DTO
     * @return Boolean
     */
    @Override
    public Boolean deleteCourse(CourseRemoveDTO courseRemoveDTO) {
        Course one = this.getOne(Wrappers.<Course>lambdaQuery().eq(Course::getId, courseRemoveDTO.getId()));
        if (one != null && one.getLessonCount() == 0 && TeachingConstant.COURSE_PUBLISH_STATUS_UNPUBLISH.equals(one.getPublishStatus())) {
            this.removeById(one);
            traceLogUtils.saveTraceLog(null, one, TeachingTypeEnum.COURSE.getCode(), DatabaseOperationTypeEnum.DELETE.getCode());
            return Boolean.TRUE;
        }
        throw new CheckedException("课程不存在或课程已发布");
    }

    /**
     * 查询课程表
     *
     * @param page           分页信息
     * @param courseQueryDTO 查询条件，查询课程表DTO
     * @return Page<CourseVO>
     */
    @Override
    public Page<CourseVO> listCourses(Page page, CourseQueryDTO courseQueryDTO) {
        Page<Course> coursePage = this.page(page, Wrappers.<Course>lambdaQuery()
                .eq(Opt.ofNullable(courseQueryDTO.getStageId()).isPresent(), Course::getStageId, courseQueryDTO.getStageId())
                .eq(Opt.ofNullable(courseQueryDTO.getDisable()).isPresent(), Course::getDisable, courseQueryDTO.getDisable())
                .eq(Opt.ofNullable(courseQueryDTO.getPublishStatus()).isPresent(), Course::getPublishStatus, courseQueryDTO.getPublishStatus())
                .eq(Objects.nonNull(courseQueryDTO.getCourseTypeId()), Course::getCourseTypeId, courseQueryDTO.getCourseTypeId())
                .like(Opt.ofBlankAble(courseQueryDTO.getCourseName()).isPresent(), Course::getCourseName, courseQueryDTO.getCourseName())
                .orderByDesc(Course::getId)
        );

        // 获取所有课程类型
        List<CourseType> courseTypes = courseTypeService.list();
        Map<Long, String> courseTypeMap = courseTypes.stream()
                .collect(Collectors.toMap(
                        courseType -> courseType.getId().longValue(),
                        CourseType::getName
                ));

        List<CourseVO> list = coursePage.getRecords().stream()
                .map(course -> {
                    CourseVO courseVO = BeanUtil.copyProperties(course, CourseVO.class);

                    // 设置课程类型名称
                    courseVO.setCourseTypeName(courseTypeMap.get(course.getCourseTypeId()));

                    // 设置收费方式
                    courseVO.setChargeMethod(course.getChargeMethod());

                    // 设置课时费标准
                    if (Objects.equals(ChargeMethodEnum.TYPE_0.getCode(), course.getChargeMethod())) { // 门店设置
                        courseVO.setCourseFee("-");
                    } else { // 自定义
                        courseVO.setCourseFee(Optional.ofNullable(course.getCustomizeFee())
                                .map(Object::toString)
                                .orElse("-"));
                    }

                    // 获取门店授权数量
                    long authCount = courseAuthStoreService.count(
                            Wrappers.lambdaQuery(CourseAuthStore.class)
                                    .eq(CourseAuthStore::getCourseId, course.getId())
                    );
                    courseVO.setAuthStoreCount((int) authCount);

                    return courseVO;
                })
                .toList();

        // 查询最新的发布时间
        Set<Integer> collect = list.stream().map(CourseVO::getId).collect(Collectors.toSet());
        List<CourseVersion> courseVersions = courseVersionMapper.selectList(new QueryWrapper<CourseVersion>()
                .in(!collect.isEmpty(), COURSE_ID, collect)
                .groupBy(COURSE_ID)
                .select(COURSE_ID, "MAX(create_time) as create_time"));

        courseVersions.forEach(courseVersion -> list.stream().filter(courseVO -> courseVO.getId().equals(courseVersion.getCourseId()))
                .forEach(courseVO -> courseVO.setPublishTime(courseVersion.getCreateTime())));

        return new Page<CourseVO>(coursePage.getCurrent(), coursePage.getSize(), coursePage.getTotal()).setRecords(list);
    }

    /**
     * 根据ID查询
     *
     * @param courseQueryDTO 查询课程表DTO
     * @return CourseVO
     */
    @Override
    public CourseVO getCourse(CourseQueryDTO courseQueryDTO) {
        Course course = this.getOne(Wrappers.<Course>lambdaQuery().eq(Course::getId, courseQueryDTO.getId()));
        if (Opt.ofNullable(course).isEmpty()) {
            throw new BizException("课程id错误");
        }
        CourseVO courseVO = BeanUtil.copyProperties(course, CourseVO.class);
        courseVO.setLessonCount(lessonService.getLessonCount(courseQueryDTO.getId()).intValue());
        courseVO.setChargeMethod(course.getChargeMethod());
        courseVO.setCourseTypeId(course.getCourseTypeId());
        courseVO.setCourseFee(Optional.ofNullable(course.getCustomizeFee())
                .map(Object::toString)
                .orElse("0.00"));
        CourseVersion courseVersion = courseVersionMapper.selectOne(new QueryWrapper<CourseVersion>()
                .eq("course_id", course.getId())
                .groupBy(COURSE_ID)
                .select(COURSE_ID, "MAX(create_time) as create_time"));
        if (Opt.ofNullable(courseVersion).isPresent()) {
            courseVO.setPublishTime(courseVersion.getCreateTime());
        }
        return courseVO;
    }

    /**
     * 获取序列号
     *
     * @return Integer
     */
    private Integer getSerialNumber() {
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();

        // 尝试从 redis 中获取序列号
        Integer serialNumber = Convert.toInt(valueOperations.get(TeachingConstant.COURSE_SERIAL_NUMBER));

        if (serialNumber != null) {
            return Convert.toInt(valueOperations.increment(TeachingConstant.COURSE_SERIAL_NUMBER));
        }

        log.info("从数据库中获取序列号");
        serialNumber = fetchSerialNumber();
        return serialNumber;
    }

    /**
     * 从数据库中获取最新序列号
     *
     * @return 最新的序列号
     */
    private Integer fetchSerialNumber() {
        // 不存在序列号，从数据库中获取课程code
        String lastCode = courseMapper.selectLastCode();

        // 数据库为空时，返回1
        if (Opt.ofNullable(lastCode).isEmpty()) {
            setNumber(TeachingConstant.SERIAL_NUMBER);
            return TeachingConstant.SERIAL_NUMBER;
        }

        int length = lastCode.length();
        // 新的月份，返回1
        String s = CharSequenceUtil.sub(lastCode, length - 5, length - 3);
        if (DateUtil.month(new Date()) + 1 != Convert.toInt(s)) {
            setNumber(TeachingConstant.SERIAL_NUMBER);
            return TeachingConstant.SERIAL_NUMBER;
        }

        // 截取课程编号
        String sub = CharSequenceUtil.sub(lastCode, length - 3, length);
        int serialNumber = Convert.toInt(sub) + 1;

        // 缓存到 redis 中，缓存时间设置为当月剩余时间，单位秒
        setNumber(serialNumber);
        return serialNumber;
    }

    /**
     * 设置序列号到Redis缓存
     *
     * @param serialNumber 序列号
     */
    private void setNumber(int serialNumber) {
        redisTemplate.opsForValue().set(
                TeachingConstant.COURSE_SERIAL_NUMBER,
                Integer.toString(serialNumber),
                this.getRemainingSecondsInMonth(),
                TimeUnit.SECONDS);
    }


    /**
     * 获取当月剩余时间，单位秒
     *
     * @return 剩余的秒数
     */
    public long getRemainingSecondsInMonth() {
        // 获取当前日期
        Date now = new Date();

        // 获取当月最后一天
        DateTime endOfMonth = DateUtil.endOfMonth(now);

        // 将剩余天数转换为秒，并返回
        return DateUtil.between(now, endOfMonth, DateUnit.SECOND);
    }


    /**
     * 发布课程
     *
     * @param coursePublishDTO 课程发布类
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int coursePublish(CoursePublishDTO coursePublishDTO) {
        // 查询当前版本（还未发布最新版本时）
        Integer version = this.getById(coursePublishDTO.getCourseId()).getVersion();

        // 判断是否修改课程阶段
        if (ObjectUtil.isNotEmpty(version)) {
            CoursePub coursePub = coursePubMapper.selectOne(Wrappers.lambdaQuery(CoursePub.class)
                    .eq(CoursePub::getCourseId, coursePublishDTO.getCourseId())
                    .eq(CoursePub::getVersion, version));
            if (!coursePublishDTO.getStageId().equals(coursePub.getStageId())) {
                throw new BizException("已发布的课程,不能修改课程阶段");
            }
        }

        // 处理课程费用
        Course course = this.getById(coursePublishDTO.getCourseId());
        // 检查是否需要处理课程费用：
        // 1. syncCourseFee为true时，每次发布都处理
        // 2. syncCourseFee为false时，只在首次发布时处理
        if ((syncCourseFee || course.getVersion() == null)
            && ChargeMethodEnum.TYPE_1.getCode().equals(course.getChargeMethod())) {

            if(Objects.isNull(course.getCustomizeFee())||course.getCustomizeFee().compareTo(BigDecimal.ZERO)<0){
                throw new BizException("自定义课时费不能为空并且不允许小于0！");
            }
            // 获取当前已授权的门店列表
            List<CourseAuthStore> currentAuthStores = courseAuthStoreService.list(
                Wrappers.lambdaQuery(CourseAuthStore.class)
                        .eq(CourseAuthStore::getCourseId, coursePublishDTO.getCourseId())
            );

            if (CollUtil.isNotEmpty(currentAuthStores)) {
                List<Long> storeIds = currentAuthStores.stream()
                                                       .map(CourseAuthStore::getStoreId)
                                                       .toList();

                // 处理已授权门店的课程费用
                handleCourseFeeForNewAuthStores(coursePublishDTO.getCourseId(), storeIds, course);
            }
        }

        //更新课程为发布状态
        if (updateCoursePublish(coursePublishDTO) == 1) {
            //添加版本发布记录
            CourseVersion courseVersion = new CourseVersion();
            courseVersion.setCourseId(coursePublishDTO.getCourseId());
            courseVersion.setStatus(TeachingConstant.COURSE_PUBLISH_STATUS_PUBLISH);
            courseVersionMapper.insert(courseVersion);

            //更新课程版本和发布时间
            this.update(Wrappers.lambdaUpdate(Course.class)
                    .eq(Course::getId, coursePublishDTO.getCourseId())
                    .set(Course::getVersion, courseVersion.getId())
                    .set(Course::getPublishTime, courseVersion.getCreateTime()));

            //查询当前课程下可发布的课节信息列表
            List<LessonEntity> canPublishLessonList = getCanPublicLesson(coursePublishDTO);

            //可发布的课节Id列表
            List<Long> canPublishIdList = canPublishLessonList.stream().map(LessonEntity::getId).toList();

            //可发布的课节数量
            int canPublishLessonCount = 0;
            //课程版本Id
            Integer courseVersionId = courseVersion.getId();
            if (!canPublishIdList.isEmpty()) {
                canPublishLessonCount = canPublishIdList.size();
                //更新课节为发布状态
                int result = lessonService.updateLessonByList(canPublishIdList);
                //课节发布成功后，添加课节发布记录
                if (result > 0) {
                    insertLessonPub(canPublishLessonList, courseVersionId);
                }
            }
            //记录课程发布信息
            insertCoursePub(coursePublishDTO, courseVersionId, canPublishLessonCount);
        }
        //发布成功时,发送消息
        coursePubManager.sendPublishCourseMessage(coursePublishDTO.getCourseId());
        return 1;
    }

    /**
     * 更新课程为发布状态
     *
     * @param coursePublishDTO 课程发布dto
     */
    private int updateCoursePublish(CoursePublishDTO coursePublishDTO) {
        LambdaQueryWrapper<Course> courseQueryWrapper = Wrappers.lambdaQuery();
        courseQueryWrapper.eq(Course::getId, coursePublishDTO.getCourseId());
        Course oldCourse = courseMapper.selectOne(courseQueryWrapper);

        LambdaUpdateWrapper<Course> courseUpdateWrapper = Wrappers.lambdaUpdate();
        //更新条件
        courseUpdateWrapper.eq(Course::getId, coursePublishDTO.getCourseId());
        //要修改的信息
        courseUpdateWrapper.set(Course::getPublishStatus, TeachingConstant.COURSE_PUBLISH_STATUS_PUBLISH);
        courseUpdateWrapper.set(Course::getCourseName, coursePublishDTO.getCourseName());
        courseUpdateWrapper.set(Course::getStageId, coursePublishDTO.getStageId());

        Course newCourse = new Course();
        BeanUtil.copyProperties(oldCourse, newCourse);
        newCourse.setPublishStatus(TeachingConstant.COURSE_PUBLISH_STATUS_PUBLISH);
        traceLogUtils.saveTraceLog(newCourse, oldCourse, TeachingTypeEnum.COURSE.getCode(), DatabaseOperationTypeEnum.UPDATE.getCode());
        return courseMapper.update(courseUpdateWrapper);
    }


    /**
     * 添加课节发布记录
     *
     * @param lessonEntityList 课节信息
     * @param courseVersionId  课程版本Id
     */
    public void insertLessonPub(List<LessonEntity> lessonEntityList, Integer courseVersionId) {
        //记录课节发布信息
        List<LessonPubEntity> lessonPubEntityList = new ArrayList<>();
        lessonEntityList.forEach(lessonEntity -> {
            LessonPubEntity lessonPubEntity = new LessonPubEntity();
            BeanUtil.copyProperties(lessonEntity, lessonPubEntity);
            lessonPubEntity.setVersion(courseVersionId);
            lessonPubEntity.setLessonId(lessonEntity.getId());
            //不使用复制过来的id,使用自动生产的id
            lessonPubEntity.setId(null);
            //课节发布已发布
            lessonPubEntity.setPublishStatus(TeachingConstant.LESSON_PUBLISH_STATUS_PUBLISH);
            lessonPubEntityList.add(lessonPubEntity);
        });
        lessonPubService.saveBatch(lessonPubEntityList);
    }


    /**
     * 添加课程发布记录
     *
     * @param coursePublishDTO    课程发布dto
     * @param courseVersionId     课程Id
     * @param canPublishLessonNum 可发布的课节数量
     */
    public void insertCoursePub(CoursePublishDTO coursePublishDTO, Integer courseVersionId, Integer canPublishLessonNum) {
        // 获取当前课程的完整信息
        Course course = this.getById(coursePublishDTO.getCourseId());

        CoursePub coursePub = new CoursePub();
        BeanUtil.copyProperties(coursePublishDTO, coursePub);
        coursePub.setVersion(courseVersionId);
        coursePub.setLessonCount(canPublishLessonNum);
        coursePub.setPublishStatus(TeachingConstant.COURSE_PUBLISH_STATUS_PUBLISH);

        // 复制新增字段
        coursePub.setCourseTypeId(course.getCourseTypeId());
        coursePub.setChargeMethod(course.getChargeMethod());
        coursePub.setCustomizeFee(course.getCustomizeFee());
        coursePubService.save(coursePub);
    }

    /**
     * 查询可发布课节列表
     *
     * @param coursePublishDTO 课程发布dto
     * @return 可发布的课节列表
     */
    public List<LessonEntity> getCanPublicLesson(CoursePublishDTO coursePublishDTO) {

        //已符合发布条件的课节
        List<LessonEntity> canPublishList = lessonMapper.selectList(Wrappers.lambdaQuery(LessonEntity.class)
                .eq(LessonEntity::getCourseId, coursePublishDTO.getCourseId())
                //可发布
                .eq(LessonEntity::getCanPublish, TeachingConstant.LESSON_CAN_PUBLISH)
                //未发布
                .eq(LessonEntity::getPublishStatus, TeachingConstant.LESSON_PUBLISH_STATUS_UNPUBLISH));

        //已发布过的课节
        canPublishList.addAll(lessonMapper.selectList(Wrappers.lambdaQuery(LessonEntity.class)
                .eq(LessonEntity::getCourseId, coursePublishDTO.getCourseId())
                //已发布
                .eq(LessonEntity::getPublishStatus, TeachingConstant.LESSON_PUBLISH_STATUS_PUBLISH)));
        //全部课节
        log.info("全部课节:{}", JSON.toJSONString(canPublishList));
        return canPublishList;
    }


    /**
     * 获取已发布最新版本的课程列表
     *
     * @return List<CourseVO>
     */
    @Override
    public List<CourseVO> getCourseList() {
        //根据发布时间倒序和阶段id顺序排序
        List<Course> courseList = this.list(Wrappers.lambdaQuery(Course.class)
                .eq(Course::getPublishStatus, TeachingConstant.COURSE_PUBLISH_STATUS_PUBLISH)
                .eq(Course::getDisable, TeachingConstant.COURSE_NOT_DISABLE)
                .orderByAsc(Course::getStageId)
                .orderByDesc(Course::getPublishTime));

        // 没有数据的时候返回空集合
        if (CollUtil.isEmpty(courseList)) {
            return Collections.emptyList();
        }

        //根据id和版本获取信息
        List<CoursePub> coursePubList = coursePubMapper.selectList(Wrappers.lambdaQuery(CoursePub.class)
                .in(CoursePub::getCourseId, courseList.stream().map(Course::getId).toList()));

        // 创建一个Map，按courseId和version组合键存储CoursePub
        Map<String, CoursePub> coursePubMap = coursePubList.stream()
                .collect(Collectors.toMap(
                        coursePub -> coursePub.getCourseId() + "_" + coursePub.getVersion(),
                        coursePub -> coursePub
                ));

        List<CourseVO> courseVOList = new ArrayList<>();

        courseList.forEach(course -> {
            CourseVO courseVO = new CourseVO();

            // 构建组合键
            String key = course.getId() + "_" + course.getVersion();

            // 从map中获取匹配的CoursePub
            CoursePub coursePub = coursePubMap.get(key);
            BeanUtil.copyProperties(coursePub, courseVO, "id");
            courseVO.setId(coursePub.getCourseId());

            courseVO.setPublishTime(course.getPublishTime());

            courseVOList.add(courseVO);
        });

        return courseVOList;
    }

    /**
     * 根据课程名称获取已发布课程列表
     *
     * @param courseName 课程名称
     * @return List<CourseVO>
     */
    @Override
    public List<CourseVO> getCourseListByName(String courseName) {
        List<CoursePub> coursePubList = coursePubMapper.selectList(Wrappers.lambdaQuery(CoursePub.class)
                .like(CharSequenceUtil.isNotBlank(courseName), CoursePub::getCourseName, courseName)
                .orderByDesc(CoursePub::getUpdateTime));
        //根据时间顺序去重已发布课程 每个课程只取最新一条的版本
        Set<Integer> processedCourseIds = new HashSet<>();
        List<CoursePub> uniqueCoursePubList = new ArrayList<>();

        coursePubList.forEach(coursePub -> {
            if (!processedCourseIds.contains(coursePub.getCourseId())) {
                uniqueCoursePubList.add(coursePub);
                processedCourseIds.add(coursePub.getCourseId());
            }
        });

        List<CourseVO> courseVOList = new ArrayList<>();

        uniqueCoursePubList.forEach(coursePub -> {
            CourseVO courseVO = new CourseVO();
            BeanUtil.copyProperties(coursePub, courseVO, "id");
            courseVO.setId(coursePub.getCourseId());
            courseVOList.add(courseVO);
        });

        return courseVOList;
    }

    /**
     * 根据id列表获取最新已发布的课程列表
     *
     * @param idList id列表
     * @return 最新已发布的课程列表
     */
    @Override
    public List<CourseVO> getCourseListByIds(List<Integer> idList) {
        //根据发布时间倒序和阶段id顺序排序
        List<Course> courseList = this.list(Wrappers.lambdaQuery(Course.class)
                .eq(Course::getPublishStatus, TeachingConstant.COURSE_PUBLISH_STATUS_PUBLISH)
                .in(CollUtil.isNotEmpty(idList), Course::getId, idList)
                .orderByAsc(Course::getStageId)
                .orderByDesc(Course::getPublishTime));

        // 没有数据的时候返回空集合
        if (CollUtil.isEmpty(courseList)) {
            return Collections.emptyList();
        }

        //根据id和版本获取信息
        List<CoursePub> coursePubList = coursePubMapper.selectList(Wrappers.lambdaQuery(CoursePub.class)
                .in(CoursePub::getCourseId, courseList.stream().map(Course::getId).toList()));

        // 创建一个Map，按courseId和version组合键存储CoursePub
        Map<String, CoursePub> coursePubMap = coursePubList.stream()
                .collect(Collectors.toMap(
                        coursePub -> coursePub.getCourseId() + "_" + coursePub.getVersion(),
                        coursePub -> coursePub
                ));

        List<CourseVO> courseVOList = new ArrayList<>();

        courseList.forEach(course -> {
            CourseVO courseVO = new CourseVO();

            // 构建组合键
            String key = course.getId() + "_" + course.getVersion();

            // 从map中获取匹配的CoursePub
            CoursePub coursePub = coursePubMap.get(key);
            BeanUtil.copyProperties(coursePub, courseVO, "id");
            courseVO.setId(coursePub.getCourseId());

            courseVO.setPublishTime(course.getPublishTime());

            courseVOList.add(courseVO);
        });

        return courseVOList;
    }

    @Override
    public Map<Long, CourseVO> getCourseMapByIdList(List<Long> courseIdList) {
        // 查询所有符合条件的记录
        LambdaQueryWrapper<CoursePub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CoursePub::getCourseId, courseIdList)
                .eq(CoursePub::getDelFlag, 0);

        List<CoursePub> coursePubList = coursePubMapper.selectList(queryWrapper);

        // 分组并找到每个 course_id 下版本号最大的记录
        Map<Long, CoursePub> latestVersions = new HashMap<>();

        for (CoursePub coursePub : coursePubList) {
            Long courseId = (long) coursePub.getCourseId();
            Integer version = coursePub.getVersion();

            latestVersions.computeIfAbsent(courseId, k -> coursePub);
            CoursePub existing = latestVersions.get(courseId);
            if (version > existing.getVersion()) {
                latestVersions.put(courseId, coursePub);
            }
        }

        // 将 CoursePub 对象转换为 CourseVO 对象
        return latestVersions.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> BeanUtil.copyProperties(entry.getValue(), CourseVO.class)
                ));
    }

    /**
     * 设置课程是否停用
     *
     * @param id      课程id
     * @param disable 是否停用(0未停用，1停用)
     * @return Boolean
     */
    @Override
    public Boolean updateCourseDisable(Integer id, Integer disable) {
        return this.update(Wrappers.lambdaUpdate(Course.class)
                .eq(Course::getId, id)
                .set(disable.equals(TeachingConstant.COURSE_NOT_DISABLE), Course::getDisable, TeachingConstant.COURSE_NOT_DISABLE)
                .set(disable.equals(TeachingConstant.COURSE_IS_DISABLE), Course::getDisable, TeachingConstant.COURSE_IS_DISABLE));
    }

    /**
     * 根据课程版本集合获取课程列表
     *
     * @param versionList 课程版本集合
     * @return List<CourseVO>
     */
    @Override
    public List<CourseVO> getCourseListByVersion(List<Integer> versionList) {
        if (CollUtil.isEmpty(versionList)) {
            return List.of();
        }
        List<CoursePub> coursePubList = coursePubMapper.selectList(Wrappers.lambdaQuery(CoursePub.class)
                .in(CoursePub::getVersion, versionList));

        List<CourseVO> courseVOList = new ArrayList<>();
        coursePubList.forEach(coursePub -> {
            CourseVO courseVO = new CourseVO();
            BeanUtil.copyProperties(coursePub, courseVO, "id");
            courseVO.setId(coursePub.getCourseId());
            courseVOList.add(courseVO);
        });
        return courseVOList;
    }

    /**
     * 根据课程阶段ID获取相关课程信息
     *
     * @param courseDTO
     * @return java.util.List<com.yuedu.teaching.vo.CourseVO>
     * <AUTHOR>
     * @date 2025/4/22 13:31
     */
    @Override
    public List<CourseVO> getCourseListByStageId(CourseDTO courseDTO) {
        List<CoursePub> coursePubList =
                coursePubMapper.selectList(
                        Wrappers.lambdaQuery(CoursePub.class)
                                .like(Objects.nonNull(courseDTO.getStageId()), CoursePub::getStageId, courseDTO.getStageId())
                                .orderByDesc(CoursePub::getUpdateTime));
        // 根据时间顺序去重已发布课程 每个课程只取最新一条的版本
        Set<Integer> processedCourseIds = new HashSet<>();
        List<CoursePub> uniqueCoursePubList = new ArrayList<>();

        coursePubList.forEach(
                coursePub -> {
                    if (!processedCourseIds.contains(coursePub.getCourseId())) {
                        uniqueCoursePubList.add(coursePub);
                        processedCourseIds.add(coursePub.getCourseId());
                    }
                });

        List<CourseVO> courseVOList = new ArrayList<>();

        uniqueCoursePubList.forEach(
                coursePub -> {
                    CourseVO courseVO = new CourseVO();
                    BeanUtil.copyProperties(coursePub, courseVO, "id");
                    courseVO.setId(coursePub.getCourseId());
                    courseVOList.add(courseVO);
                });

        return courseVOList;
    }

    /**
     * 获取授权校区
     *
     * @param courseId
     * @return java.util.List<com.yuedu.teaching.vo.CampusAuthVO>
     * <AUTHOR>
     * @date 2025/5/21 17:11
     */
    @Override
    public List<CampusAuthVO> getAuthStore(Long courseId) {
        // 1. 获取所有校区信息
        CampusDTO campusDTO = new CampusDTO();
        campusDTO.setCampusType(Integer.parseInt(SchoolCampusEnum.CAMPUS.getType()));
        List<CampusVO> allCampus = remoteCampusService.getCampusAll(campusDTO).getData();

        if (courseId == null) {
            // 如果未传入courseId，直接返回所有校区信息
            return allCampus.stream()
                    .map(
                            campus -> {
                                CampusAuthVO authVO = new CampusAuthVO();
                                BeanUtils.copyProperties(campus, authVO);
                                authVO.setIsAuthorized(false);
                                return authVO;
                            })
                    .collect(Collectors.toList());
        }

        // 2. 获取课程已授权的校区ID列表
        List<Long> authorizedStoreIds =
                courseAuthStoreService
                        .list(
                                Wrappers.lambdaQuery(CourseAuthStore.class)
                                        .eq(CourseAuthStore::getCourseId, courseId))
                        .stream()
                        .map(CourseAuthStore::getStoreId)
                        .toList();

        // 3. 组装返回结果，标记已授权的校区
        return allCampus.stream()
                .map(
                        campus -> {
                            CampusAuthVO authVO = new CampusAuthVO();
                            BeanUtils.copyProperties(campus, authVO);
                            authVO.setIsAuthorized(authorizedStoreIds.contains(campus.getId()));
                            return authVO;
                        })
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> getCourseIdByType(Long courseTypeId) {
        if (courseTypeId == null) {
            return List.of();
        }
        return this.coursePubService.list(Wrappers.lambdaQuery(CoursePub.class)
                .eq(CoursePub::getCourseTypeId, courseTypeId))
                .stream()
                .map(CoursePub::getCourseId)
                .toList();
    }

    @Override
    public List<CourseVO> getCourseListByStoreId(CourseQueryDTO courseQueryDTO) {

        List<Long> courseIds = courseAuthStoreService.listCurrentAuthCourseIds(courseQueryDTO.getCourseTypeId(),
                courseQueryDTO.getStoreId());
        if(CollectionUtil.isEmpty(courseIds)){
            return Collections.emptyList();
        }

        //根据发布时间倒序和阶段id顺序排序
        List<Course> courseList = this.list(Wrappers.lambdaQuery(Course.class)
                .eq(Course::getPublishStatus, TeachingConstant.COURSE_PUBLISH_STATUS_PUBLISH)
                .eq(Course::getDisable, TeachingConstant.COURSE_NOT_DISABLE)
                .eq(Objects.nonNull(courseQueryDTO.getCourseTypeId()),Course::getCourseTypeId, courseQueryDTO.getCourseTypeId())
                .in(Course::getId, courseIds)
                .orderByAsc(Course::getStageId)
                .orderByDesc(Course::getPublishTime));

        // 没有数据的时候返回空集合
        if (CollUtil.isEmpty(courseList)) {
            return Collections.emptyList();
        }

        //根据id和版本获取信息
        List<CoursePub> coursePubList = coursePubMapper.selectList(Wrappers.lambdaQuery(CoursePub.class)
                .in(CoursePub::getCourseId, courseList.stream().map(Course::getId).toList()));

        // 创建一个Map，按courseId和version组合键存储CoursePub
        Map<String, CoursePub> coursePubMap = coursePubList.stream()
                .collect(Collectors.toMap(
                        coursePub -> coursePub.getCourseId() + "_" + coursePub.getVersion(),
                        coursePub -> coursePub
                ));

        List<CourseVO> courseVOList = new ArrayList<>();

        courseList.forEach(course -> {
            CourseVO courseVO = new CourseVO();

            // 构建组合键
            String key = course.getId() + "_" + course.getVersion();

            // 从map中获取匹配的CoursePub
            CoursePub coursePub = coursePubMap.get(key);
            BeanUtil.copyProperties(coursePub, courseVO, "id");
            courseVO.setId(coursePub.getCourseId());

            courseVO.setPublishTime(course.getPublishTime());

            courseVOList.add(courseVO);
        });

        return courseVOList;
    }

    @Override
    public Page<CourseVO> listCoursesByStoreId(Page<Course> page, CourseQueryDTO courseQueryDTO) {
        List<Long> courseIds = courseAuthStoreService.listCurrentAuthCourseIds(courseQueryDTO.getCourseTypeId(), StoreContextHolder.getStoreId());
        if (CollectionUtil.isEmpty(courseIds)) {
            return new Page<CourseVO>(page.getCurrent(), page.getSize(), 0)
                    .setRecords(Collections.emptyList());
        }
        Page<Course> coursePage = this.page(page, Wrappers.<Course>lambdaQuery()
                        .in(Course::getId, courseIds)
                .eq(Opt.ofNullable(courseQueryDTO.getStageId()).isPresent(), Course::getStageId, courseQueryDTO.getStageId())
                .eq(Opt.ofNullable(courseQueryDTO.getDisable()).isPresent(),Course::getDisable,courseQueryDTO.getDisable())
                .eq(Opt.ofNullable(courseQueryDTO.getPublishStatus()).isPresent(),Course::getPublishStatus, courseQueryDTO.getPublishStatus())
                .like(Opt.ofBlankAble(courseQueryDTO.getCourseName()).isPresent(), Course::getCourseName, courseQueryDTO.getCourseName())
                .orderByDesc(Course::getId)
        );

        // 获取所有课程类型
        List<CourseType> courseTypes = courseTypeService.list();
        Map<Long, String> courseTypeMap = courseTypes.stream()
                .collect(Collectors.toMap(
                        courseType -> courseType.getId().longValue(),
                        CourseType::getName
                ));

        List<CourseVO> list = coursePage.getRecords().stream()
                .map(course -> {
                    CourseVO courseVO = BeanUtil.copyProperties(course, CourseVO.class);

                    // 设置课程类型名称
                    courseVO.setCourseTypeName(courseTypeMap.get(course.getCourseTypeId()));

                    return courseVO;
                })
                .toList();

        // 查询最新的发布时间
        Set<Integer> collect = list.stream().map(CourseVO::getId).collect(Collectors.toSet());
        List<CourseVersion> courseVersions = courseVersionMapper.selectList(new QueryWrapper<CourseVersion>()
                .in(!collect.isEmpty(), COURSE_ID, collect)
                .groupBy(COURSE_ID)
                .select(COURSE_ID, "MAX(create_time) as create_time"));

        courseVersions.forEach(courseVersion -> list.stream().filter(courseVO -> courseVO.getId().equals(courseVersion.getCourseId()))
                .forEach(courseVO -> courseVO.setPublishTime(courseVersion.getCreateTime())));

        return new Page<CourseVO>(coursePage.getCurrent(), coursePage.getSize(), coursePage.getTotal()).setRecords(list);
    }
}
