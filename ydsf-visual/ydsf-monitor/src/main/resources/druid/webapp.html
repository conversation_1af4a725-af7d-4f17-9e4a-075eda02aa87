<!doctype html>
<html>
	<head>
		<title>Druid WebApp Stat</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf8" />
		<link href='css/bootstrap.min.css' rel="stylesheet" />
		<link href="css/style.css" type="text/css" rel="stylesheet"/>
		<script src="js/doT.js" type="text/javascript" charset="utf8"></script>
    	<script type="text/javascript" src="js/jquery.min.js"></script>
    	<script type="text/javascript" src="js/bootstrap.min.js"></script>
		<script src="js/lang.js" type="text/javascript" charset="utf8"></script>
		<script src="js/common.js" type="text/javascript" charset="utf8"></script>
	</head>
	<body>
		
    	<div class="container-fluid">
      		<div class="row-fluid">
        		<div class="span12">
          				<h3>
          					WebAppStat List
          					<a href="webapp.json" target="_blank">View JSON API</a>
          				</h3>
        		</div>
      		</div> 
    	</div>
		<script type="text/template" id="webapp-template">
				{{~ it.content :contextNow:i  }}
				<ul class="nav nav-tabs" id="datasourceTab{{=i}}">
					<li class="active">
						<a 	href="webapp.html#dstab/{{=contextNow.ContextPath}}">WebApp-{{=contextNow.ContextPath}}</a>
					</li>
				</ul>
				<div class="tab-content">
					<div class="tab-pane active" id="dstab/{{=contextNow.ContextPath}}">
						<table class="table table-bordered" style="background-color: #fff">
							<tbody>
								{{  for(var key in contextNow ) {  }}									
									<tr>
										<td valign="top" class="td_lable lang" langKey="{{=key}}">{{=key}}</td>
										<td>{{=contextNow[key]}}</td>
									</tr>									
								{{ } }}
							</tbody>
						</table>
					</div>
				</div>
				{{~ }}	
		</script>
    	<script type="text/javascript">
			$.namespace("druid.datasource");
			druid.datasource = function () {  
				
				return  {
					init : function() {
						druid.common.buildHead(4);
						this.ajaxRequestForBasicInfo();
					},
					ajaxRequestForBasicInfo : function() {
						$.ajax({
							type: 'POST',
							url: "webapp.json",
							success: function(data) {
								var datasourceList = data.Content;
								

								var tmpl = $('#webapp-template').html();
								var contents = {'content' : datasourceList};
								var doTtmpl = doT.template(tmpl);
								var contentHtml = doTtmpl(contents);
								
								$(".span12 h3").after(contentHtml);
								
								$('#datasourceTab a').click(function (e) {
						            e.preventDefault();
						            $(this).tab('show');
						    	});
								druid.lang.trigger();
							},
							dataType: "json"
						});
					}
					
					
				}
			}();
	
			$(document).ready(function() {
				druid.datasource.init();
			});
		</script>
	</body>
</html>
