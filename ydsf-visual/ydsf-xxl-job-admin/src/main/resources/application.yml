# 此配置只适合开发测试环境，详细配置参考： http://t.cn/A64RaHJm
server:
  port: 9080
  servlet:
    context-path: /xxl-job-admin

spring:
  profiles:
    active: @profiles.active@
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:ydsf-register}:${NACOS_PORT:8848}
        namespace: ${NACOS_NAMESPACE:public}
        metadata:
          management.context-path: ${server.servlet.context-path}/actuator
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml
  mvc:
    static-path-pattern: /static/**
  freemarker:
    suffix: .ftl
    request-context-attribute: request
    settings:
      number_format: 0.##########
    template-loader-path: classpath:/templates/
  mail:
    host: smtp.mxhichina.com
    port: 465
    from: <EMAIL>
    username: <EMAIL>
    password: xxxx
    properties:
      mail:
        smtp:
          auth: true
          ssl.enable: true
          starttls.enable: false
          required: false
